import React, { useState, useEffect } from 'react';
import { Modal, Table, Collapse, Input, Button, Alert } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { MdOutlineRefresh } from 'react-icons/md';
import moment from 'moment';

const { Panel } = Collapse;

// Helper function to safely format date
const formatDate = (dateString) => {
    try {
        return moment(dateString).format('DD MMM YYYY, hh:mm A');
    } catch (error) {
        console.error('Error formatting date:', error);
        return 'Invalid Date';
    }
};

const EditableLineItemsRevisionModal = ({
    visible,
    onClose,
    dataSource,
    onRestore,
}) => {
    const [revisionData, setRevisionData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [expandedPanels, setExpandedPanels] = useState({});
    const [searchTerm, setSearchTerm] = useState('');

    const columns = [
        { title: 'ITEM', dataIndex: 'item' },
        { title: 'ELEVATION', dataIndex: 'elevation' },
        { title: 'WIDTH(MM)', dataIndex: 'width' },
        { title: 'HEIGHT(MM)', dataIndex: 'height' },
        {
            title: 'SQFT',
            dataIndex: 'sqft',
            render: (val) => <span className="gx-text-orange">{val}</span>,
        },
        { title: 'PRICE', dataIndex: 'price' },
        {
            title: 'TOTAL',
            dataIndex: 'total',
            render: (val) => (
                <b className="gx-fs-md gx-p-1 gx-text-orange">{val}</b>
            ),
        },
    ];

    const renderSection = (title, items = [], defaultOpen = false) => {
        const totalAmount = items.reduce((sum, item) => {
            const amount = parseFloat(
                (item?.total || '0').replace(/[^0-9.-]+/g, '')
            );
            return sum + (isNaN(amount) ? 0 : amount);
        }, 0);

        const totalSqft = items
            .reduce((sum, item) => {
                const sqft = parseFloat(item?.sqft || 0);
                return sum + (isNaN(sqft) ? 0 : sqft);
            }, 0)
            .toFixed(2);

        return (
            <Collapse
                key={title}
                defaultActiveKey={defaultOpen ? ['1'] : []}
                className="gx-mb-3"
            >
                <Panel
                    key="1"
                    className="wy-eli-collapse-no-padding"
                    header={
                        <span>
                            {title}
                            <span className="gx-ml-2">
                                ({items.length} items)
                            </span>
                        </span>
                    }
                    extra={
                        <div>
                            <div className="gx-mr-3">
                                <span className="wy-fw-400 gx-mr-1">
                                    Total ₹
                                </span>
                                <b>
                                    {totalAmount.toLocaleString('en-IN', {
                                        maximumFractionDigits: 2,
                                        minimumFractionDigits: 2,
                                    })}
                                </b>
                            </div>
                            <div>
                                <span className="wy-fw-400 gx-mr-1">
                                    Total Quantity
                                </span>
                                <b>{totalSqft}</b>
                            </div>
                        </div>
                    }
                >
                    <Table
                        className="wy-eli-table gx-p-3"
                        bordered
                        dataSource={items}
                        columns={columns}
                        rowKey="key"
                        scroll={{ x: 'max-content' }}
                        pagination={{
                            position: ['bottomRight'],
                            pageSizeOptions: ['10', '25', '50', '100'],
                            defaultPageSize: 10,
                        }}
                    />
                    <div className="wy-eli-footer">
                        <div>
                            * Every line item in this group will be addressed as{' '}
                            <b>{title} Item</b> and labelled as{' '}
                            <b>[Item] - [Elevation]</b>
                        </div>
                        {(title === 'Kitchen' ||
                            title === 'Other SQFT Based Items') && (
                            <div>
                                * Sqft will be calculated as{' '}
                                <b>[(Width(mm)) * (Height(mm))] / 93903.04</b>
                            </div>
                        )}
                    </div>
                </Panel>
            </Collapse>
        );
    };

    // Toggle panel expansion
    const handlePanelChange = (index) => {
        setExpandedPanels((prev) => ({
            ...prev,
            [index]: !prev[index],
        }));
    };

    // Load revision data on mount
    useEffect(() => {
        try {
            const loadData = async () => {
                const currentData = Array.isArray(dataSource)
                    ? [...dataSource]
                    : [];

                const revisions = [
                    {
                        date: new Date(Date.now() - 86400000).toISOString(), // Yesterday
                        sections: {
                            kitchen: [
                                {
                                    key: '1',
                                    item: 'Base Unit',
                                    elevation: 'A',
                                    width: '600',
                                    height: '720',
                                    sqft: '4.60',
                                    price: '900',
                                    total: '4,140.00',
                                },
                            ],
                            cabinets: [],
                            sqftBased: [],
                            qtyBased: [],
                        },
                    },
                ];

                setRevisionData(revisions);
                setLoading(false);
            };

            loadData();
        } catch (err) {
            console.error('Error loading revision data:', err);
            setError('Failed to load revision history');
            setLoading(false);
        }
    }, [dataSource]);

    const handleSearch = (e) => {
        setSearchTerm(e.target.value.toLowerCase());
    };

    const filteredRevisions = revisionData
        .map((version) => {
            if (!version || !version.sections) return version;

            const filteredSections = {};
            Object.entries(version.sections).forEach(([section, items]) => {
                filteredSections[section] = items.filter(
                    (item) =>
                        item &&
                        item.item &&
                        item.item.toLowerCase().includes(searchTerm)
                );
            });

            return { ...version, sections: filteredSections };
        })
        .filter((version) =>
            version.sections
                ? Object.values(version.sections).some(
                      (items) => items && items.length > 0
                  )
                : false
        );

    if (!visible) return null;

    return (
        <Modal
            title="Line Items Revision History"
            visible={visible}
            onCancel={onClose}
            width={1200}
            footer={[
                <Button key="close" onClick={onClose}>
                    Close
                </Button>,
            ]}
        >
            {error && (
                <Alert
                    type="error"
                    showIcon
                    className="gx-mb-3"
                    message="Error"
                    description={error}
                />
            )}

            {loading ? (
                <div className="gx-text-center gx-p-4">
                    Loading revision history...
                </div>
            ) : filteredRevisions.length === 0 ? (
                <div className="gx-text-center gx-p-4">
                    No revision history found
                </div>
            ) : (
                <div className="wy-eli-revision-container">
                    {filteredRevisions.map((version, index) => (
                        <div key={index} className="gx-mb-4">
                            <Collapse
                                className="wy-eli-revision-collapse gx-mb-4"
                                onChange={() => handlePanelChange(index)}
                                activeKey={
                                    expandedPanels[index] ? ['panel'] : []
                                }
                            >
                                <Panel
                                    key="panel"
                                    className="wy-eli-revision-panel"
                                    header={
                                        <div className="wy-eli-revision-header gx-d-flex gx-justify-content-between gx-align-items-center">
                                            <h4 className="gx-mb-0">
                                                {formatDate(version.date)}
                                            </h4>
                                        </div>
                                    }
                                    extra={
                                        <Button
                                            size="small"
                                            type="primary"
                                            className="gx-mb-0"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                onRestore(
                                                    version.sections?.kitchen ||
                                                        []
                                                );
                                            }}
                                        >
                                            <MdOutlineRefresh className="gx-mr-2" />{' '}
                                            Restore
                                        </Button>
                                    }
                                >
                                    <div className="gx-mb-4">
                                        {renderSection(
                                            'Kitchen',
                                            version.sections?.kitchen || []
                                        )}
                                        {renderSection(
                                            'Cabinets',
                                            version.sections?.cabinets || []
                                        )}
                                        {renderSection(
                                            'Other SQFT Based Items',
                                            version.sections?.sqftBased || []
                                        )}
                                        {renderSection(
                                            'Other QTY Based Items',
                                            version.sections?.qtyBased || []
                                        )}
                                    </div>
                                </Panel>
                            </Collapse>
                        </div>
                    ))}
                </div>
            )}
        </Modal>
    );
};

export default EditableLineItemsRevisionModal;
