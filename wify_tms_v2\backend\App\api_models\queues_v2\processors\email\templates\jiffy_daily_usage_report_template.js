class jiffy_daily_usage_report_template {
    jiffyDailyUsageReportTemplate = (notifyData) => {
        const rows = notifyData?.data || [];

        // Desired column order
        const preferredOrder = [
            'Vertical',
            'Status update(overall)',
            'Status Update (Via JIFFY)',
            'JIFFY Utilization',
        ];

        let content = '';

        if (rows.length > 0) {
            // In case there are extra keys in the data, append them after preferred ones
            const allKeys = [
                ...new Set(rows.flatMap((obj) => Object.keys(obj))),
            ];
            const columns = preferredOrder.concat(
                allKeys.filter((k) => !preferredOrder.includes(k))
            );

            // Table header
            const tableHeader = `
                <tr>
                    ${columns
                        .map(
                            (col) =>
                                `<th style="border:1px solid #ccc; padding:5px; text-align:left">${col}</th>`
                        )
                        .join('')}
                </tr>
            `;

            // Table rows
            const tableBody = rows
                .map(
                    (row) => `
                <tr>
                    ${columns
                        .map(
                            (col) =>
                                `<td style="border:1px solid #ccc; padding:5px">${row[col] ?? ''}</td>`
                        )
                        .join('')}
                </tr>
            `
                )
                .join('');

            content = `
                <p>Please find below JIFFY usage for bulk status update of service requests via SP</p>
                <table style="border-collapse: collapse; border:1px solid #ccc">
                    ${tableHeader}
                    ${tableBody}
                </table>
            `;
        } else {
            content = `
                <p>No status updates were done during this yesterday.</p>
            `;
        }

        return `
        <html lang="en">
        <body>
            <div>
                Hello User,
                <br /><br />
                ${content}
                <br /><br />                
                Regards,<br />
                Wiffy Tech.
            </div>
        </body>
        </html>`;
    };
}

module.exports = new jiffy_daily_usage_report_template();
