import React, { useState } from 'react';
import {
    Table,
    Collapse,
    Input,
    Button,
    Space,
    Switch,
    Popconfirm,
    Select,
    Tag,
} from 'antd';
import {
    PlusOutlined,
    MinusOutlined,
    SearchOutlined,
    DownloadOutlined,
    FileTextOutlined,
    EditOutlined,
    EyeOutlined,
} from '@ant-design/icons';
import EditableLineItemsRevisionModal from './EditableLineItemsRevisionModal';
import './ELI.css';

const { Panel } = Collapse;

const generateData = (count) => {
    const baseItems = [
        {
            item: 'Tall unit',
            elevation: 'A',
            width: 626,
            height: 2080,
            sqft: 14.02,
        },
        {
            item: 'Base cabinet',
            elevation: 'B',
            width: 1488,
            height: 882,
            sqft: 14.13,
        },
        {
            item: 'Wall cabinet',
            elevation: 'B',
            width: 875,
            height: 600,
            sqft: 5.65,
        },
        {
            item: 'Base cabinet',
            elevation: 'C',
            width: 1853,
            height: 882,
            sqft: 17.59,
        },
        {
            item: 'Wall cabinet',
            elevation: 'C',
            width: 1547,
            height: 600,
            sqft: 9.99,
        },
        { item: 'Wall cabinet', elevation: 'C', width: 0, height: 0, sqft: 0 },
    ];

    const result = [];
    for (let i = 0; i < count; i++) {
        const base = baseItems[i % baseItems.length];
        result.push({
            key: i + 1,
            ...base,
            price: '',
            total: 0,
        });
    }
    return result;
};

const EditableLineItems = () => {
    const [editMode, setEditMode] = useState(true);
    const [isLargeDataset, setIsLargeDataset] = useState(false);
    const [revisionModalVisible, setRevisionModalVisible] = useState(false);
    const [dataSource, setDataSource] = useState(generateData(7));
    const [editingRows, setEditingRows] = useState(new Set());
    const [originalData, setOriginalData] = useState({});
    const [searchTerms, setSearchTerms] = useState({
        kitchen: '',
        cabinets: '',
        sqftBased: '',
        qtyBased: '',
        installation: '',
        brandCabinets: '',
    });

    // Function to handle search input changes
    const handleSearch = (section, value) => {
        setSearchTerms((prev) => ({
            ...prev,
            [section]: value.toLowerCase(),
        }));
    };

    // Function to filter data based on search term
    const filterData = (data, searchTerm) => {
        if (!searchTerm) return data;
        return data.filter(
            (item) => item.item && item.item.toLowerCase().includes(searchTerm)
        );
    };

    const handlePriceChange = (value, record) => {
        const updatedData = dataSource.map((item) =>
            item.key === record.key
                ? {
                      ...item,
                      price: value,
                      total: (value * item.sqft).toFixed(2),
                  }
                : item
        );
        setDataSource(updatedData);
    };

    const handleEdit = (record) => {
        setEditingRows((prev) => new Set(prev).add(record.key));
        setOriginalData((prev) => ({
            ...prev,
            [record.key]: { ...record },
        }));
    };

    const handleSave = (key) => {
        setEditingRows((prev) => {
            const newSet = new Set(prev);
            newSet.delete(key);
            return newSet;
        });
        setOriginalData((prev) => {
            const newData = { ...prev };
            delete newData[key];
            return newData;
        });
    };

    const handleCancel = (key) => {
        setDataSource((prevData) =>
            prevData.map((item) =>
                item.key === key ? originalData[key] || item : item
            )
        );
        setEditingRows((prev) => {
            const newSet = new Set(prev);
            newSet.delete(key);
            return newSet;
        });
        setOriginalData((prev) => {
            const newData = { ...prev };
            delete newData[key];
            return newData;
        });
    };

    const handleDatasetToggle = (checked) => {
        setIsLargeDataset(checked);
        setDataSource(generateData(checked ? 400 : 7));
    };

    const handleOpenRevision = () => {
        setRevisionModalVisible(true);
    };

    const handleCloseRevision = () => {
        setRevisionModalVisible(false);
    };

    const handleRestoreData = (restoredData) => {
        setDataSource(restoredData);
        setRevisionModalVisible(false);
        // You might want to add a success message here
        console.log('Data restored successfully');
    };

    const itemOptions = [
        { value: 'Tall unit', label: 'Tall unit' },
        { value: 'Base cabinet', label: 'Base cabinet' },
        { value: 'Wall cabinet', label: 'Wall cabinet' },
        { value: 'Drawer unit', label: 'Drawer unit' },
        { value: 'Corner cabinet', label: 'Corner cabinet' },
    ];

    const baseColumns = [
        {
            title: 'ITEM',
            dataIndex: 'item',
            render: (text, record) => {
                if (editMode) {
                    return (
                        <Select
                            value={text}
                            onChange={(value) => {
                                const newData = [...dataSource];
                                const index = newData.findIndex(
                                    (item) => record.key === item.key
                                );
                                if (index > -1) {
                                    newData[index] = {
                                        ...record,
                                        item: value,
                                    };
                                    setDataSource(newData);
                                }
                            }}
                            className="gx-w-100"
                            options={itemOptions}
                        />
                    );
                }
                return text;
            },
        },
        {
            title: 'ELEVATION',
            dataIndex: 'elevation',
            width: 150,
            render: (text, record) => {
                if (editMode) {
                    return (
                        <Input
                            value={text}
                            onChange={(e) => {
                                const newData = [...dataSource];
                                const index = newData.findIndex(
                                    (item) => record.key === item.key
                                );
                                if (index > -1) {
                                    newData[index] = {
                                        ...record,
                                        elevation: e.target.value,
                                    };
                                    setDataSource(newData);
                                }
                            }}
                            className="gx-w-100"
                        />
                    );
                }
                return text;
            },
        },
        {
            title: 'WIDTH(MM)',
            dataIndex: 'width',
            width: 150,
            render: (text, record) => {
                if (editMode) {
                    return (
                        <Input
                            type="number"
                            value={text}
                            onChange={(e) => {
                                const newData = [...dataSource];
                                const index = newData.findIndex(
                                    (item) => record.key === item.key
                                );
                                if (index > -1) {
                                    newData[index] = {
                                        ...record,
                                        width: Number(e.target.value),
                                    };
                                    setDataSource(newData);
                                }
                            }}
                            className="gx-w-100"
                        />
                    );
                }
                return text;
            },
        },
        {
            title: 'HEIGHT(MM)',
            dataIndex: 'height',
            width: 150,

            render: (text, record) => {
                if (editMode) {
                    return (
                        <Input
                            type="number"
                            value={text}
                            onChange={(e) => {
                                const newData = [...dataSource];
                                const index = newData.findIndex(
                                    (item) => record.key === item.key
                                );
                                if (index > -1) {
                                    newData[index] = {
                                        ...record,
                                        height: Number(e.target.value),
                                    };
                                    setDataSource(newData);
                                }
                            }}
                            className="gx-w-100"
                        />
                    );
                }
                return text;
            },
        },
        {
            title: 'SQFT',
            dataIndex: 'sqft',
            render: (val) => <span className="gx-text-orange">{val}</span>,
        },
        {
            title: 'PRICE',
            dataIndex: 'price',
            width: 300,
            render: (text, record) =>
                editMode ? (
                    <Input
                        value={text}
                        onChange={(e) =>
                            handlePriceChange(Number(e.target.value), record)
                        }
                        placeholder="Enter price"
                        className="gx-w-100"
                    />
                ) : (
                    <span>{text || '-'}</span>
                ),
        },
        {
            title: 'TOTAL',
            dataIndex: 'total',
            width: 200,
            render: (val) => (
                <span className="gx-fs-md gx-p-1 gx-text-orange gx-fs-18">
                    {val}
                </span>
            ),
        },
    ];

    const actionColumn = {
        title: 'ACTIONS',
        key: 'actions',
        width: 120,
        render: (_, record) => {
            const isEditing = editingRows.has(record.key);
            const isChanged =
                isEditing && originalData[record.key]?.price !== record.price;
            const saveBtnClass = isEditing
                ? `wy-eli--save-btn ${!isChanged ? 'wy-eli--save-btn-disabled' : ''}`
                : 'wy-eli--save-btn';

            return (
                <Space size="small">
                    <Button
                        type="primary"
                        shape="circle"
                        icon={<PlusOutlined />}
                        onClick={() =>
                            isEditing
                                ? handleSave(record.key)
                                : handleEdit(record)
                        }
                        disabled={isEditing && !isChanged}
                        size="small"
                        className={`wy-eli--action-btn ${saveBtnClass}`}
                    />
                    <Button
                        type="primary"
                        shape="circle"
                        icon={<MinusOutlined />}
                        onClick={() =>
                            isEditing
                                ? handleCancel(record.key)
                                : (() => {
                                      const newData = dataSource.filter(
                                          (item) => item.key !== record.key
                                      );
                                      setDataSource(newData);
                                  })()
                        }
                        size="small"
                        className={`wy-eli--action-btn ${isEditing ? 'wy-eli--cancel-btn' : 'wy-eli--delete-btn'}`}
                    />
                </Space>
            );
        },
    };

    const columns = editMode ? [...baseColumns, actionColumn] : baseColumns;

    return (
        <div>
            <Space style={{ marginBottom: 16 }}>
                <Button
                    size="small"
                    className="gx-mb-0"
                    icon={<EditOutlined />}
                    type={editMode ? 'primary' : 'default'}
                    onClick={() => setEditMode(true)}
                >
                    Edit Mode
                </Button>
                <Button
                    size="small"
                    className="gx-mb-0"
                    icon={<EyeOutlined />}
                    type={!editMode ? 'primary' : 'default'}
                    onClick={() => setEditMode(false)}
                >
                    View Only
                </Button>
                <Space>
                    <Switch
                        size="large"
                        checked={isLargeDataset}
                        onChange={handleDatasetToggle}
                        checkedChildren="Large Dataset (400 Items)"
                        unCheckedChildren="Small Dataset (7 Items)"
                    />
                </Space>
            </Space>
            <div className="wy-eli-service-header">
                <div className=" gx-d-flex gx-align-items-center gx-justify-content-between gx-mb-3">
                    <div className="gx-fs-lg" style={{ fontWeight: 500 }}>
                        Service provider line items
                    </div>
                    <Space>
                        <Button
                            size="small"
                            type="primary"
                            icon={<DownloadOutlined />}
                            className="gx-mb-0"
                        >
                            Download
                        </Button>
                        <Button
                            size="small"
                            icon={<FileTextOutlined />}
                            className="gx-mb-0"
                            onClick={handleOpenRevision}
                        >
                            Revision
                        </Button>
                    </Space>
                </div>

                {/* Kitchen Section */}
                <Collapse
                    defaultActiveKey={['1']}
                    className="wy-eli-collapse gx-mb-3"
                >
                    <Panel
                        header={
                            <span>
                                Kitchen{' '}
                                <span className="gx-ml-2">
                                    ({dataSource.length} items)
                                </span>
                            </span>
                        }
                        className="wy-eli-collapse-no-padding"
                        key="1"
                        extra={
                            <div>
                                <div>
                                    <span className="wy-fw-400 gx-mr-1">
                                        Total :
                                    </span>
                                    <b>₹0.00</b>
                                </div>
                                <div>
                                    <span className="wy-fw-400 gx-mr-1">
                                        Total Quantity :
                                    </span>
                                    <b>61.38</b>
                                </div>
                            </div>
                        }
                    >
                        <div className="wy-eli-search-input">
                            <Input
                                placeholder="Search by name in Kitchen...."
                                prefix={<SearchOutlined />}
                                value={searchTerms.kitchen}
                                onChange={(e) =>
                                    handleSearch('kitchen', e.target.value)
                                }
                            />
                        </div>
                        <Table
                            className="wy-eli-table"
                            bordered
                            dataSource={filterData(
                                dataSource,
                                searchTerms.kitchen
                            )}
                            columns={columns}
                            pagination={{
                                pageSize: isLargeDataset ? 50 : 10,
                                showSizeChanger: false,
                            }}
                        />
                        <div className="wy-eli-footer">
                            <div>
                                * Every line item in this group will be
                                addressed as <b>Kitchen Item</b> and labelled as{' '}
                                <b>[Item] - [Elevation]</b>
                            </div>
                            <div>
                                * Sqft will be calculated as{' '}
                                <b>[(Width(mm)) * (Height(mm))] / 93903.04</b>
                            </div>
                        </div>
                    </Panel>
                </Collapse>

                {/* Cabinets Section */}
                <Collapse
                    defaultActiveKey={['1']}
                    className="wy-eli-collapse gx-mb-3"
                >
                    <Panel
                        header={
                            <span>
                                Cabinets{' '}
                                <span className="gx-ml-2">(0 items)</span>
                            </span>
                        }
                        className="wy-eli-collapse-no-padding"
                        key="1"
                        extra={
                            <div>
                                <div>
                                    <span className="wy-fw-400 gx-mr-1">
                                        Total :
                                    </span>
                                    <b>₹0.00</b>
                                </div>
                                <div>
                                    <span className="wy-fw-400 gx-mr-1">
                                        Total Quantity :
                                    </span>
                                    <b>0.00</b>
                                </div>
                            </div>
                        }
                    >
                        <div className="wy-eli-search-input">
                            <Input
                                placeholder="Search by name in Cabinets...."
                                prefix={<SearchOutlined />}
                                value={searchTerms.cabinets}
                                onChange={(e) =>
                                    handleSearch('cabinets', e.target.value)
                                }
                            />
                        </div>
                        <Table
                            className="wy-eli-table"
                            bordered
                            dataSource={filterData([], searchTerms.cabinets)}
                            columns={columns}
                            pagination={{
                                pageSize: isLargeDataset ? 50 : 10,
                                showSizeChanger: false,
                            }}
                        />
                        <div className="wy-eli-footer">
                            <div>
                                * Every line item in this group will be
                                addressed as <b>Cabinet Item</b> and labelled as{' '}
                                <b>[Item] - [Elevation]</b>
                            </div>
                        </div>
                    </Panel>
                </Collapse>

                {/* Other SQFT Based Items Section */}
                <Collapse
                    defaultActiveKey={['1']}
                    className="wy-eli-collapse gx-mb-3"
                >
                    <Panel
                        header={
                            <span>
                                Other SQFT Based Items{' '}
                                <span className="gx-ml-2">(0 items)</span>
                            </span>
                        }
                        className="wy-eli-collapse-no-padding"
                        key="1"
                        extra={
                            <div>
                                <div>Total ₹0.00</div>
                                <div>Total Quantity 0.00</div>
                            </div>
                        }
                    >
                        <div className="wy-eli-search-input">
                            <Input
                                placeholder="Search by name in Other SQFT Based Items...."
                                prefix={<SearchOutlined />}
                                value={searchTerms.sqftBased}
                                onChange={(e) =>
                                    handleSearch('sqftBased', e.target.value)
                                }
                            />
                        </div>
                        <Table
                            className="wy-eli-table"
                            bordered
                            dataSource={filterData([], searchTerms.sqftBased)}
                            columns={columns}
                            pagination={{
                                pageSize: isLargeDataset ? 50 : 10,
                                showSizeChanger: false,
                            }}
                        />
                        <div className="wy-eli-footer">
                            <div>
                                * Every line item in this group will be
                                addressed as <b>SQFT Based Item</b> and labelled
                                as <b>[Item] - [Elevation]</b>
                            </div>
                        </div>
                    </Panel>
                </Collapse>

                {/* Other QTY Based Items Section */}
                <Collapse
                    defaultActiveKey={['1']}
                    className="wy-eli-collapse gx-mb-3"
                >
                    <Panel
                        header={
                            <span>
                                Other QTY Based Items{' '}
                                <span className="gx-ml-2">(0 items)</span>
                            </span>
                        }
                        className="wy-eli-collapse-no-padding"
                        key="1"
                        extra={
                            <div>
                                <div>
                                    <span className="wy-fw-400 gx-mr-1">
                                        Total :
                                    </span>
                                    <b>₹0.00</b>
                                </div>
                                <div>
                                    <span className="wy-fw-400 gx-mr-1">
                                        Total Quantity :
                                    </span>
                                    <b>0.00</b>
                                </div>
                            </div>
                        }
                    >
                        <div className="wy-eli-search-input">
                            <Input
                                placeholder="Search by name in Other QTY Based Items...."
                                prefix={<SearchOutlined />}
                                value={searchTerms.qtyBased}
                                onChange={(e) =>
                                    handleSearch('qtyBased', e.target.value)
                                }
                            />
                        </div>
                        <Table
                            className="wy-eli-table"
                            bordered
                            dataSource={filterData([], searchTerms.qtyBased)}
                            columns={columns}
                            pagination={{
                                pageSize: isLargeDataset ? 50 : 10,
                                showSizeChanger: false,
                            }}
                        />
                        <div className="wy-eli-footer">
                            <div>
                                * Every line item in this group will be
                                addressed as <b>QTY Based Item</b> and labelled
                                as <b>[Item] - [Elevation]</b>
                            </div>
                        </div>
                    </Panel>
                </Collapse>
            </div>

            {/* Brand Line Items Section */}
            <div className="wy-eli-service-header gx-mt-4">
                <div className="">
                    <div className="gx-d-flex gx-align-items-center gx-justify-content-between gx-mb-3">
                        <div className="gx-fs-lg">Brand Line Items</div>
                        <Space>
                            <Button
                                size="small"
                                type="primary"
                                icon={<DownloadOutlined />}
                                className="gx-mb-0"
                            >
                                Download
                            </Button>
                            <Button
                                size="small"
                                icon={<FileTextOutlined />}
                                className="gx-mb-0"
                            >
                                Revision
                            </Button>
                        </Space>
                    </div>
                </div>

                {/* Installation Section */}
                <Collapse
                    defaultActiveKey={['1']}
                    className="wy-eli-collapse gx-mb-3"
                >
                    <Panel
                        header={
                            <span>
                                Installation
                                <span className="gx-ml-2">(0 items)</span>
                            </span>
                        }
                        className="wy-eli-collapse-no-padding"
                        key="1"
                        extra={
                            <div>
                                <div>
                                    <span className="wy-fw-400 gx-mr-1">
                                        Total :
                                    </span>
                                    <b>₹0.00</b>
                                </div>
                                <div>
                                    <span className="wy-fw-400 gx-mr-1">
                                        Total Quantity :
                                    </span>
                                    <b>0.00</b>
                                </div>
                            </div>
                        }
                    >
                        <div className="wy-eli-search-input">
                            <Input
                                placeholder="Search by name in Installation...."
                                prefix={<SearchOutlined />}
                                value={searchTerms.installation}
                                onChange={(e) =>
                                    handleSearch('installation', e.target.value)
                                }
                            />
                        </div>
                        <Table
                            className="wy-eli-table"
                            bordered
                            dataSource={filterData(
                                [],
                                searchTerms.installation
                            )}
                            columns={columns}
                            pagination={{
                                pageSize: isLargeDataset ? 50 : 10,
                                showSizeChanger: false,
                            }}
                        />
                        <div className="wy-eli-footer">
                            <div>
                                * Every line item in this group will be
                                addressed as <b>Installation Item</b> and
                                labelled as <b>[Item] - [Category]</b>
                            </div>
                        </div>
                    </Panel>
                </Collapse>

                {/* Brand Cabinets Section */}
                <Collapse
                    defaultActiveKey={['1']}
                    className="wy-eli-collapse gx-mb-3"
                >
                    <Panel
                        header={
                            <span>
                                Brand Cabinets
                                <span className="gx-ml-2">(0 items)</span>
                            </span>
                        }
                        className="wy-eli-collapse-no-padding"
                        key="1"
                        extra={
                            <div>
                                <div>
                                    <span className="wy-fw-400 gx-mr-1">
                                        Total :
                                    </span>
                                    <b>₹0.00</b>
                                </div>
                                <div>
                                    <span className="wy-fw-400 gx-mr-1">
                                        Total Quantity :
                                    </span>
                                    <b>0.00</b>
                                </div>
                            </div>
                        }
                    >
                        <div className="wy-eli-search-input">
                            <Input
                                placeholder="Search by name in Brand Cabinets...."
                                prefix={<SearchOutlined />}
                                value={searchTerms.brandCabinets}
                                onChange={(e) =>
                                    handleSearch(
                                        'brandCabinets',
                                        e.target.value
                                    )
                                }
                            />
                        </div>
                        <Table
                            className="wy-eli-table"
                            bordered
                            dataSource={filterData(
                                [],
                                searchTerms.brandCabinets
                            )}
                            columns={columns}
                            pagination={{
                                pageSize: isLargeDataset ? 50 : 10,
                                showSizeChanger: false,
                            }}
                        />
                        <div className="wy-eli-footer">
                            <div>
                                * Every line item in this group will be
                                addressed as <b>Brand Cabinet</b> and labelled
                                as <b>[Item] - [Category]</b>
                            </div>
                        </div>
                    </Panel>
                </Collapse>
            </div>

            <EditableLineItemsRevisionModal
                visible={revisionModalVisible}
                onClose={handleCloseRevision}
                dataSource={dataSource}
                onRestore={handleRestoreData}
            />
        </div>
    );
};

export default EditableLineItems;
