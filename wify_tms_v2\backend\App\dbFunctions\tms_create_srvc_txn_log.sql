CREATE OR REPLACE FUNCTION public.tms_create_srvc_txn_log(srvc_req_id_ bigint, status_key_ text, txn_date timestamp without time zone, srvc_type_id_ integer, old_status_key text, form_data json, txn_reason text DEFAULT ''::text)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare
	status boolean;
	message text;
	resp_data json;
	ip_address_ text;
	user_agent_ text;
	org_id_ integer;
	usr_id_ uuid;

	affected_rows integer;
	ins_id bigint;
	existing_db_id bigint;
	_temp_is_agent_update bool;

begin

	status = false;
	message = 'Internal_error';
	org_id_ = json_extract_path_text(form_data,'org_id');
	usr_id_ = json_extract_path_text(form_data,'usr_id');
	ip_address_ = json_extract_path_text(form_data,'ip_address');
	user_agent_ = json_extract_path_text(form_data,'user_agent');
	_temp_is_agent_update = (form_data->>'is_agent_update')::bool;

	select db_id 
	  from cl_tx_srvc_req_trnstn_log txn_log 
	 where srvc_req_id = srvc_req_id_
	   and srvc_type_id = srvc_type_id_
	   and status_key = status_key_
	  into existing_db_id ;
	 
	raise notice 'Existing ID %', existing_db_id;
	
	if existing_db_id > 0 then
		raise notice 'Transition date -  %', txn_date;
		
		if _temp_is_agent_update is null then
			_temp_is_agent_update = false;
		end if;

		-- do update 
		update public.cl_tx_srvc_req_trnstn_log
		   set trnstn_date = 
		   		case 
		   			when txn_date is null then trnstn_date
		   			else txn_date -- keep as is
		   		end,
		       trnstn_reason = txn_reason,
		       u_meta = row(ip_address_,user_agent_,now() at time zone 'utc'),
		       u_by = usr_id_,
		       is_updated_via_jiffy = _temp_is_agent_update
		 where db_id = existing_db_id;
		
		get diagnostics affected_rows = ROW_COUNT;
	
		if affected_rows = 1 then
			resp_data =  json_build_object('entry_id',existing_db_id);
		end if;  			
		   
	else
		-- do insert
		INSERT INTO public.cl_tx_srvc_req_trnstn_log(
			srvc_req_id, status_key, trnstn_date, d_spent, trnstn_reason, c_by, c_meta, srvc_type_id, is_updated_via_jiffy)
			VALUES (
				srvc_req_id_,
				status_key_, 
				txn_date, 
				0, 
				txn_reason, 
				usr_id_, 
				row(ip_address_,user_agent_,now() at time zone 'utc'),
				srvc_type_id_,
				_temp_is_agent_update
			)
		returning db_id into ins_id;
		
		get diagnostics affected_rows = ROW_COUNT;
--		raise notice 'Affected rows - %',  affected_rows;
		if affected_rows = 1 then
--			Now insert statuses
			resp_data =  json_build_object('entry_id',ins_id);
		end if;
	end if;
	
	-- update days spent in previous stage
	update cl_tx_srvc_req_trnstn_log 
	   set d_spent = EXTRACT(DAY FROM now() at time zone 'utc' - trnstn_date) + 1
	 where srvc_req_id = srvc_req_id_
	   and srvc_type_id = srvc_type_id_
	   and status_key = old_status_key ;
	   
	get diagnostics affected_rows = ROW_COUNT;

	raise notice 'Days spent updated for logs %',affected_rows;

	status = true;
	message = 'success';
	
	return json_build_object('status',status,'code',message,'data',resp_data);
	
end;

$function$
;
