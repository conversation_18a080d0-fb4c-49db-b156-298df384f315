CREATE OR REPLACE FUNCTION public.tms_get_task_updates_dumps_fr_usr(requester_info json, filter_ json)
 RETURNS SETOF json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
-- 	Bare minimums
	status boolean;
	message text;
	resp_data json;
	ip_address_ text;
	user_agent_ text;
	org_id_ integer;
	usr_id_ uuid;
	organization_info_json json;
	general_info_json json;

--temp
	matching_ids_json json;
	matching_ids bigint[];
	temp_id bigint;
	is_sp_custom_field boolean;
	sp_custom_fields_json json;
	verticals_list_ integer[];
	_single_col_details_fr_sp_attachment text;
	is_sp_attachment boolean;
	custom_field_column text;
	filters_json json;

--Output
	total integer;
--  Temps
	_dynamic_columns text;
	_single_col_key text;
	_single_col_name text;
	_single_col_extracter text;
	field_label_mapping json;
    _single_col_details json;
    is_organization_info_json boolean;
    is_general_info_json boolean;
   	is_status_field_info boolean;
   	sbtsk_type_id_ integer;
   	loc_groups_data json;
   	sbtsk_status_wise_fields_ json;
   	sbtsk_status_key text;
   	sbtsk_status_key_fr_join text;
   	dynamic_join text default '';
   	dynamic_group_by text default '';
	org_timezone text;

-- 	Output
	_dynamic_sql text;

	begin
		status = false;
		message = 'Internal_error';
	
		org_id_ = json_extract_path_text(requester_info,'org_id');
		usr_id_ = json_extract_path_text(requester_info,'usr_id');
		ip_address_ = json_extract_path_text(requester_info,'ip_address');
		user_agent_ = json_extract_path_text(requester_info,'user_agent');
		sbtsk_type_id_ = (requester_info->>'sbtsk_type_id')::int;
		field_label_mapping = requester_info->'field_label_mapping';
		filters_json = json_extract_path_text(requester_info,'filters');
		verticals_list_ = array( select json_array_elements_text(json_extract_path(filters_json,'verticals_list')) )::integer[];
		org_timezone = tms_hlpr_get_org_timezone(org_id_);

		matching_ids_json = (
			select tms_get_task_updates_by_filter(requester_info, filter_, '')
		);
	
		sbtsk_status_wise_fields_ = array_to_json(array(
		    select jsonb_build_object(
		        'status_key', sbtsk_statuses.status_key,
		        'status_fields',
		            CASE 
		                WHEN nullif(sbtsk_types.form_data->>('sbtsk_status_' || sbtsk_statuses.status_key || '_fields'), '') IS NULL
		                    THEN NULL
		                ELSE ((sbtsk_types.form_data->>('sbtsk_status_' || sbtsk_statuses.status_key || '_fields'))::jsonb#>>'{translatedFields}')::jsonb
		            END
		    )
		    from cl_cf_sbtsk_statuses as sbtsk_statuses
		    left join cl_cf_sbtsk_types as sbtsk_types
		        on sbtsk_types.sbtsk_type_id = sbtsk_statuses.sbtsk_type_id
		    where sbtsk_statuses.sbtsk_type_id = sbtsk_type_id_
		    group by sbtsk_statuses.db_id, sbtsk_types.sbtsk_type_id
		));

		SELECT array_agg((value->>'id')::int) 
		  FROM json_array_elements(matching_ids_json) AS value
		  into matching_ids;
		 
		if cardinality(verticals_list_) > 0 then
			 select ((sp_custom_fields.settings_data#>>'{sp_cust_fields_json}')::jsonb#>>'{translatedFields}')::jsonb
			   from cl_tx_orgs_settings as sp_custom_fields 
			  where sp_custom_fields.db_id = verticals_list_[1]
			   into sp_custom_fields_json;
		end if;
	   	
		 
		_dynamic_columns = '';

		organization_info_json = '[{"key":"service_type", "label":"Service Types"},
									{"key":"brand_name", "label":"Brand Name"}]';
		general_info_json = '[
								{"key":"task_start_date", "label":"Task start date"},
								{"key":"assignee_name", "label":"Assignee name"},
								{"key":"task_status", "label":"Task status"},
								{"key":"assigned_by", "label":"Assigned by"},
								{"key":"site_location_group", "label":"Site location group"}
							 ]';
							
		loc_groups_data = array_to_json(array(
       		select jsonb_build_object(
       					'id',id,
       					'groups_name',groups_name,
       					'group_data',tms_get_city_state_ex_city_from_grps(array[id])
       		       )
       		  from cl_tx_location_groups
       		 where org_id = org_id_
       		));
	
		
		FOR _single_col_key in select json_array_elements_text(requester_info->'selected_columns') loop
--	        raise notice '_single_col_key %',_single_col_key;
	       	_single_col_name = field_label_mapping->>_single_col_key;
--	       raise notice '_single_col_name %',_single_col_name;
	       
	       	_single_col_extracter = null;
	       
			
	        is_organization_info_json = false;
	       	is_general_info_json = false;
	       	is_status_field_info = false;
	       	is_sp_custom_field = false;
	       	is_sp_attachment = false;
			
		 	 -- checking for organization info fields
		 	select value
			  from json_array_elements(organization_info_json)
			 where _single_col_key = value->>'key'
			 limit 1
			  into _single_col_details;
			 
			if _single_col_details is not null then
				-- This field is from organization info
				is_organization_info_json = true;
			end if;
		
			if _single_col_details is null then
				select value
				  from json_array_elements(general_info_json)
				 where _single_col_key = value->>'key'
				 limit 1
				  into _single_col_details;
				 
				if _single_col_details is not null then
					-- This field is from organization info
					is_general_info_json = true;
				end if;
			end if;
		
			if _single_col_details is null then
			
				IF _single_col_key LIKE 'sbtskStatus_%' then
					_single_col_details := json_build_object('key', _single_col_key, 'label', _single_col_name);
				end if;
				
				if _single_col_details is not null then
					is_status_field_info = true;
				end if;
			end if;
		
			if _single_col_details is null then
			 	 -- checking custom fields for service provider 
			 	select value
				  from json_array_elements(sp_custom_fields_json)
				 where _single_col_key = value->>'key'
				 limit 1
				  into _single_col_details;
				 
				if _single_col_details is not null then
					is_sp_custom_field = true;
				end if;
			end if;
		
			if _single_col_details is null then
			
				IF _single_col_key LIKE '%_txn_time' then
					_single_col_details := json_build_object('key', _single_col_key, 'label', _single_col_name);
				
					-- get sbtsk status key from col_key
					SELECT SPLIT_PART(_single_col_key, '~', 1) into sbtsk_status_key;
					sbtsk_status_key_fr_join = 'txn_'||sbtsk_status_key;
				
					_single_col_extracter = 'TO_CHAR(('||sbtsk_status_key_fr_join||'.trnstn_date::timestamp AT TIME ZONE $$utc$$ AT TIME ZONE '||quote_literal(org_timezone)||'), $$YYYY-MM-DD hh:miPM$$)';
					dynamic_join =  dynamic_join || '
										 left join cl_tx_sbtsk_trnstn_log as ' ||sbtsk_status_key_fr_join|| '
										   on ' ||sbtsk_status_key_fr_join||'.sbtsk_id = sbtsk.db_id
										  and ' ||sbtsk_status_key_fr_join||'.status_key = ' || quote_literal(sbtsk_status_key) ||'
									 ';
					dynamic_group_by = dynamic_group_by||', ' ||sbtsk_status_key_fr_join||'.trnstn_date';
				end if;

			end if;
		
			if is_sp_custom_field is true then
			 	select value from json_array_elements_text(requester_info->'sp_attachment_field_info')
			 	 where _single_col_key = value
			 	 limit 1
			      into _single_col_details_fr_sp_attachment;
				  
			    if _single_col_details_fr_sp_attachment is not null then
					is_sp_attachment = true;
				end if;
			end if;

			if is_sp_attachment is true then
				_single_col_extracter = 'tms_hlpr_srvc_req_attachment_data_is_exists(srvc_req.form_data, $$' || _single_col_key || '$$)';
			end if;

			if is_organization_info_json is true then 

				if _single_col_details->>'key' = 'brand_name' then
				
					_single_col_extracter = 'orgs.nickname';
				
				elsif  _single_col_details->>'key' = 'service_type' then 
				
					_single_col_extracter = 'srvc_type.title';
               end if;
			end if;	
		
			if is_general_info_json is true then 

				if _single_col_details->>'key' = 'task_start_date' then
				
					_single_col_extracter = 'DATE((sbtsk.start_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)';
				
				elsif  _single_col_details->>'key' = 'assignee_name' then 
				
					_single_col_extracter = 'assignee."name"';
				
				elsif  _single_col_details->>'key' = 'task_status' then 
				
					_single_col_extracter = 'sbtsk_status.title';
				
				elsif  _single_col_details->>'key' = 'assigned_by' then 
				
					_single_col_extracter = 'assigned_by."name"';
				
				elsif  _single_col_details->>'key' = 'site_location_group' then 
				
					_single_col_extracter = 'tms_hlpr_get_loc_grps_name('|| org_id_ ||', srvc_req, $$'||(loc_groups_data)||'$$)';
               end if;
			end if;
		
			if is_status_field_info is true then
				_single_col_extracter = tms_hlpr_get_single_col_extractor_fr_task_updates(_single_col_key, sbtsk_status_wise_fields_);
			end if;

			if _single_col_details->>'widget' = 'select'
			   or _single_col_details->>'widget'= 'radio-group'
			   or _single_col_details->>'widget'='checkbox-group'then 

				if is_sp_custom_field is true then 
					custom_field_column = '($$'|| sp_custom_fields_json ||'$$)';
				end if;
				
						
				_single_col_extracter = 'array_to_string(array(
								select value->>$$label$$
									from jsonb_array_elements(
											( 
											select value
												from jsonb_array_elements('|| custom_field_column ||')
												where $$'|| (_single_col_details->>'key')::text || '$$ = value->>$$key$$
											)->$$options$$	 
										) 
									where srvc_req.form_data#>>$${' || (_single_col_details->>'key')::text ||'}$$ = value->>$$value$$
									or (
											jsonb_typeof(srvc_req.form_data->$$' || (_single_col_details->>'key')::text ||'$$) = $$array$$
											and
											value->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(srvc_req.form_data->$$' || (_single_col_details->>'key')::text ||'$$ )))
										)
								),$$,$$)';
			end if;
		
			if is_sp_custom_field is true
			   and _single_col_extracter is null then 
				_single_col_extracter = ' srvc_req.form_data->>$$' || _single_col_key || '$$ ';
			end if;
		
			if _single_col_details->>'widget' = 'date-picker' then	
				_dynamic_columns = _dynamic_columns || ', $$' || _single_col_name || '$$ , DATE((' || _single_col_extracter || ')::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$ )';   	
		   	else 
	       		_dynamic_columns = _dynamic_columns || ', $$' || _single_col_name || '$$ ,' || _single_col_extracter ;  
		    end if;

--	       raise notice '_dynamic_columns%', _dynamic_columns;

	       
	    END LOOP;
	   

		_dynamic_sql = 
				'select json_build_object(
							$$Request ID$$,srvc_req.display_code '
							|| _dynamic_columns || '
					   )
				  from cl_tx_sbtsk as sbtsk
				 inner join cl_tx_srvc_req as srvc_req 
				 	on srvc_req.db_id = sbtsk.srvc_req_id
				   and srvc_req.is_deleted is not true
			     inner join cl_cf_service_types as srvc_type
					on srvc_type.service_type_id = srvc_req.srvc_type_id	 	
				 inner join cl_tx_users as assigned_by
				 	on assigned_by.usr_id = sbtsk.c_by
				 inner join cl_tx_users as assignee 
				 	on assignee.usr_id = any(sbtsk.assigned_to) 
				 inner join cl_cf_sbtsk_statuses as sbtsk_status
				 	on sbtsk_status.status_key = sbtsk.status
				   and sbtsk_status.sbtsk_type_id = sbtsk.sbtsk_type
				 inner join cl_tx_orgs as orgs
			 		on orgs.org_id = srvc_req.org_id 
				'|| dynamic_join ||'
				 where sbtsk.is_deleted is not true
				   and sbtsk.org_id =' || org_id_ ||' 
				   and sbtsk.sbtsk_type =' || sbtsk_type_id_ || '
				   and sbtsk.db_id in (' || array_to_string(matching_ids,',') || ')
				 group by srvc_req.db_id,assignee.name, assigned_by.name, sbtsk_status.title, sbtsk.start_time, sbtsk.db_id, orgs.org_id, srvc_type.service_type_id' || dynamic_group_by;
		
		raise notice 'dump sql %', _dynamic_sql;
		
		return query execute _dynamic_sql;
	END;
$function$
;
