.wy-eli-collapse .ant-collapse-item {
    margin-bottom: 16px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.wy-eli-collapse .ant-collapse-header {
    background-color: #fafafa;
    font-weight: 500;
}

.wy-eli-collapse-no-padding
    > .ant-collapse-content
    > .ant-collapse-content-box {
    padding: 0 !important;
}

.wy-eli-collapse-no-padding .ant-table-pagination.ant-pagination {
    padding: 0 10px !important;
}

/* Action Buttons */
.wy-eli--action-btn {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 24px !important;
    height: 24px !important;
    border-radius: 50% !important;
    padding: 0 !important;
}

.wy-eli--save-btn {
    background-color: #52c41a !important;
    border-color: #52c41a !important;
    color: white !important;
}

.wy-eli--save-btn:hover {
    background-color: #73d13d !important;
    border-color: #73d13d !important;
}

.wy-eli--save-btn:disabled {
    background-color: #d9d9d9 !important;
    border-color: #d9d9d9 !important;
    cursor: not-allowed !important;
}

.wy-eli--cancel-btn,
.wy-eli--delete-btn {
    background-color: #ff4d4f !important;
    border-color: #ff4d4f !important;
    color: white !important;
}

.wy-eli--cancel-btn:hover,
.wy-eli--delete-btn:hover {
    background-color: #ff7875 !important;
    border-color: #ff7875 !important;
}

/* Button group spacing */
.wy-eli--action-btn + .wy-eli--action-btn {
    margin-left: 8px !important;
}

/* Tag Styles */
.ant-tag {
    margin: 0;
    border-radius: 4px;
    font-weight: 500;
}

.wy-eli--sqft-tag {
    color: #000 !important;
    background: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
}

.wy-eli--total-tag {
    color: #fff !important;
    background: #52c41a !important;
    border-color: #52c41a !important;
}

/* Table Styles */
.wy-eli-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.wy-eli-table .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
}

.wy-eli-table .ant-table-tbody > tr > td {
    transition: all 0.3s;
}

.wy-eli-table .ant-table-tbody > tr:hover > td {
    background: #f5f5f5;
}

/* Search Input */
.wy-eli-search-input {
    margin: 12px;
}

.wy-eli-search-input .ant-input-affix-wrapper {
    border-radius: 4px;
}

/* Footer */
.wy-eli-footer {
    background-color: #f5f5f5;
    padding: 12px 16px;
    border-top: 1px solid #e8e8e8;
    font-size: 12px;
    color: #595959;
    border-radius: 0 0 8px 8px;
}

/* Section Headers */
.wy-eli-section-header {
    margin-bottom: 16px;
}

/* Service Provider Header */
.wy-eli-service-header {
    padding: 10px;
    border: 1px solid #fa8c16;
    border-radius: 4px;
    background-color: #ffffff;
    margin-bottom: 16px;
}

/* Collapse Spacing */
.wy-eli-collapse .ant-collapse-item {
    margin-bottom: 16px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.wy-eli-collapse .ant-collapse-item + .ant-collapse-item {
    margin-top: 16px;
}

/* Buttons */
.wy-eli-btn-group {
    display: flex;
    gap: 8px;
}

.wy-eli-collapse-no-padding .ant-collapse-header {
    border-radius: 5px !important;
}
