CREATE OR REPLACE FUNCTION public.tms_ace_workflow_sync_srvc_reqs_for_hub_create_or_update(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
    -- Bare minimums
    status boolean;
    message text;
    code text;
    affected_rows integer;
    resp_data json;

    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    vertical_id_ integer;
    hub_id_ bigint;
    pincodes_ text[];
    is_active_ boolean;
    limit_ integer := 100; -- Default limit for batch processing

    -- For processing
    srvc_req_data_to_untag json;
    srvc_req_data_to_tag json;
    user_context_json json;
    hub_update_json json;
    hub_title_ text;
    total_updated_count integer := 0;
    current_srvc_req_id integer;
    current_srvc_type_id integer;
    current_hub_update_json json;
    single_req_data json;

begin
    -- Initialize status and message
    status := false;
    message := 'Internal_error';

    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    hub_id_ := (form_data_->>'hub_id')::bigint;

    -- Extract limit if provided, otherwise use default
    if form_data_->>'limit' is not null then
        limit_ := (form_data_->>'limit')::integer;
    end if;

    -- Get user context for updates
    user_context_json = tms_get_user_context_from_data(form_data_);

    -- Get hub details using hub_id
    select hub_name, vertical_id, pincodes, is_active
      from public.cl_tx_vertical_srvc_hubs
     where id = hub_id_
       and org_id = org_id_
      into hub_title_, vertical_id_, pincodes_, is_active_;

    if hub_title_ is null then
        status = false;
        message = 'hub_not_found';
        resp_data = '{}';
        return json_build_object(
            'status', status,
            'message', message,
            'data', resp_data
        );
    end if;

    -- Step 1: Get all service requests that are currently tagged to this hub
    -- but the pincode in it does not match the hub's pincodes or hub is deactivated
    select json_agg(json_build_object('req_id', db_id, 'srvc_type_id', srvc_type_id))
      from (
          select db_id, srvc_type_id
            from cl_tx_srvc_req
           where srvc_prvdr = org_id_
             and prvdr_srvc_hub = hub_id_
             and is_deleted is not true
             and (
                 -- Hub is deactivated OR pincode doesn't match hub's pincodes
                 is_active_ is not true
                 OR cust_pincode != all(pincodes_)
             )
           limit limit_
      ) limited_untag
      into srvc_req_data_to_untag;

    -- Step 2: Get all service requests that fall under this vertical
    -- and have pincode matching this hub but are not currently tagged to this hub
    -- (only if hub is active)
    if is_active_ is true then
        select json_agg(json_build_object('req_id', db_id, 'srvc_type_id', srvc_type_id))
          from (
              select db_id, srvc_type_id
                from cl_tx_srvc_req
               where srvc_prvdr = org_id_
                 and prvdr_vertical = vertical_id_
                 and (prvdr_srvc_hub is null or prvdr_srvc_hub = 0 or prvdr_srvc_hub != hub_id_)
                 and cust_pincode = any(pincodes_)
                 and cust_pincode is not null
                 and cust_pincode != ''
                 and is_deleted is not true
               limit limit_
          ) limited_tag
          into srvc_req_data_to_tag;
    end if;

    -- Process untagging (remove hub assignment)
    if srvc_req_data_to_untag is not null and json_array_length(srvc_req_data_to_untag) > 0 then
        -- Prepare the update JSON to remove hub assignment
        hub_update_json = jsonb_set(user_context_json::jsonb,'{prvdr_srvc_hub}',to_jsonb(0::bigint),true);
        hub_update_json = jsonb_set(hub_update_json::jsonb,'{prvdr_srvc_hub_title}',to_jsonb(''::text),true);

        -- Remove hub assignment from all filtered service requests
        FOR single_req_data IN SELECT * FROM json_array_elements(srvc_req_data_to_untag) LOOP
            current_srvc_req_id := (single_req_data->>'req_id')::integer;
            current_srvc_type_id := (single_req_data->>'srvc_type_id')::integer;
            current_hub_update_json := jsonb_set(hub_update_json::jsonb,'{srvc_type_id}',to_jsonb(current_srvc_type_id),true);
            perform tms_create_service_request(current_hub_update_json, current_srvc_req_id);

            raise notice 'Untagged req_id: %, srvc_type_id: %', current_srvc_req_id, current_srvc_type_id;
        END LOOP;

        total_updated_count := total_updated_count + json_array_length(srvc_req_data_to_untag);
    end if;

    -- Process tagging (assign hub to matching service requests)
    if srvc_req_data_to_tag is not null and json_array_length(srvc_req_data_to_tag) > 0 then
        -- Prepare the update JSON to assign hub
        hub_update_json = jsonb_set(user_context_json::jsonb,'{prvdr_srvc_hub}',to_jsonb(hub_id_),true);
        hub_update_json = jsonb_set(hub_update_json::jsonb,'{prvdr_srvc_hub_title}',to_jsonb(hub_title_),true);

        -- Assign hub to all matching service requests
        FOR single_req_data IN SELECT * FROM json_array_elements(srvc_req_data_to_tag) LOOP
            current_srvc_req_id := (single_req_data->>'req_id')::integer;
            current_srvc_type_id := (single_req_data->>'srvc_type_id')::integer;
            current_hub_update_json := jsonb_set(hub_update_json::jsonb,'{srvc_type_id}',to_jsonb(current_srvc_type_id),true);
            perform tms_create_service_request(current_hub_update_json, current_srvc_req_id);

            raise notice 'Tagged req_id: %, srvc_type_id: %', current_srvc_req_id, current_srvc_type_id;
        END LOOP;

        total_updated_count := total_updated_count + json_array_length(srvc_req_data_to_tag);
    end if;

    status = true;
    message = 'success';
    resp_data = json_build_object(
        'total_updated_count', total_updated_count,
        'untagged_count', coalesce(json_array_length(srvc_req_data_to_untag), 0),
        'tagged_count', coalesce(json_array_length(srvc_req_data_to_tag), 0),
        'hub_id', hub_id_,
        'hub_title', hub_title_,
        'is_active', is_active_,
        'limit_used', limit_
    );

    -- Return the result
    return json_build_object(
        'status', status,
        'message', message,
        'data', resp_data
    );
end;
$function$;
