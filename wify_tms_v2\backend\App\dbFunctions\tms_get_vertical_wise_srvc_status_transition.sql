CREATE OR REPLACE FUNCTION public.tms_get_vertical_wise_srvc_status_transition(vertical_id_ integer, materialized_table_name_ text)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
	status boolean;
	message text;
	resp_data json;
	_dynamic_columns text;
	_single_status_title text;
	_single_status_extracter text;
	all_status_titles text[];
	_dynamic_sql text;

	begin
		status = false;
		message = 'Internal_error';

		-- Get all unique status titles for the vertical's service types
		-- Group by title to handle cases where multiple status_keys have the same title
		SELECT array_agg(DISTINCT srvc_statuses.title ORDER BY srvc_statuses.title)
		  FROM cl_tx_orgs_settings as vertical_settings
		 INNER JOIN cl_cf_srvc_statuses as srvc_statuses 
		    ON srvc_statuses.srvc_id = ANY(array(SELECT json_array_elements_text(vertical_settings.settings_data->'srvc_type_id'))::integer[])
		 WHERE vertical_settings.db_id = vertical_id_
		  INTO all_status_titles;

		-- Build dynamic columns for each unique status title
		_dynamic_columns = '';
		
		FOREACH _single_status_title IN ARRAY all_status_titles
		LOOP
			_single_status_extracter = '( 
											select DATE(trnstn_log.trnstn_date) 
											  from cl_tx_srvc_req_trnstn_log as trnstn_log 
											 inner join cl_cf_srvc_statuses as status_ref
											    on status_ref.status_key = trnstn_log.status_key
											   and status_ref.srvc_id = srvc_req.srvc_type_id
										     where trnstn_log.srvc_req_id = srvc_req.db_id
										       and status_ref.title = $$'|| _single_status_title || '$$ 
										     order by trnstn_log.trnstn_date desc
										     limit 1
										  )';
			
			_dynamic_columns = _dynamic_columns || ', ' || _single_status_extracter  || ' as "' ||  _single_status_title || '" '; 
		   
	    END LOOP;

		-- Get vertical name and create materialized view
		_dynamic_sql = 'CREATE MATERIALIZED VIEW '||materialized_table_name_||' AS 				  
								select 
									vertical_settings.settings_data->>$$vertical_title$$ as "Vertical Name",
								   srvc_req.display_code as "Service Display Code"
								   ' || _dynamic_columns || '  
							      from public.cl_tx_srvc_req as srvc_req 
							     inner join cl_tx_orgs_settings as vertical_settings
							        on vertical_settings.db_id = ' || vertical_id_ || '
							       and srvc_req.srvc_type_id = ANY(array(SELECT json_array_elements_text(vertical_settings.settings_data->$$srvc_type_id$$))::integer[])
								 where srvc_req.prvdr_vertical = ' || vertical_id_ || '
								 order by srvc_req.db_id desc
					';
 	
		raise notice 'vertical status transition sql %', _dynamic_sql;
		
		execute _dynamic_sql;
		
		status = true;
		message = 'success';
		resp_data = json_build_object('materialized_table_name', materialized_table_name_);
		
		return json_build_object('status',status,'code',message,'data',resp_data);
	END;
$function$
;
