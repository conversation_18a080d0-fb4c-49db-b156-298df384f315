CREATE OR REPLACE FUNCTION public.tms_get_vertical_wise_srvc_status_transition(requester_info json, filter_ json, materialized_table_name_ text)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
-- 	Bare minimums
	status boolean;
	message text;
	resp_data json;
	ip_address_ text;
	user_agent_ text;
	org_id_ integer;
	usr_id_ uuid;
--  Specific
	srvc_type_id_ integer;	
-- 	Filters
	filter_statuses text[];

	
--  Temps
	matching_ids_json json;
	matching_ids bigint[];
	temp_id bigint;
	_dynamic_columns text;
	_single_status_key text;
	_single_status_name text;
	_single_status_extracter text;
	all_statuses json;
	all_verticals json;
	authorities_ json;
	selected_authorities_ int[] default '{}';
	user_list json; 
	prefix_authorities_key text;
	is_srvc_prvdr bool default false;
	filters_json json;
	verticals_list_ integer[];
	
-- 	Output
	_dynamic_sql text;

	begin
		status = false;
		message = 'Internal_error';
	
		org_id_ = json_extract_path_text(requester_info,'org_id');
		usr_id_ = json_extract_path_text(requester_info,'usr_id');
		ip_address_ = json_extract_path_text(requester_info,'ip_address');
		user_agent_ = json_extract_path_text(requester_info,'user_agent');
		srvc_type_id_ = json_extract_path_text(requester_info,'srvc_type_id');
		filters_json = json_extract_path_text(requester_info,'filters');
		verticals_list_ = array( select json_array_elements_text(json_extract_path(filters_json,'verticals_list')) )::integer[];
		
		filter_statuses = array( select json_array_elements_text(json_extract_path(filter_,'statuses')) )::text[];
		is_srvc_prvdr = tms_hlpr_is_org_srvc_prvdr(org_id_);
		
		-- Get all possible verticals for the organization
		all_verticals = (
			   select jsonb_object_agg(
					settings_data->>'vertical_title',
					settings_data->'srvc_type_id'
			    )
			 	from cl_tx_orgs_settings as org_vertical
			   where settings_type = 'SP_CUSTOM_FIELDS'
			     and org_id = org_id_
			     and settings_data->>'srvc_type_id' is not null
			     and settings_data->>'vertical_title' <> ''
		);

		prefix_authorities_key = 'brand_';
	    if tms_hlpr_is_org_srvc_prvdr(org_id_) then
	    	prefix_authorities_key = 'sp_';
	    end if;
	   
		authorities_ = requester_info->>(prefix_authorities_key || 'authorities');
		if authorities_ is not null then
				SELECT ARRAY(
					    SELECT CAST(regexp_replace(authorities_role, '[^0-9]', '', 'g') AS integer)
		          	     FROM json_array_elements_text(authorities_) AS authorities_role 
		          	   )
	        	  into selected_authorities_;
		end if;

		user_list = array_to_json(array(
       		select jsonb_build_object(
   					'id',users.usr_id,
   					'name',
					CASE 
                        WHEN users.is_active THEN users."name"
                        ELSE users."name" || ' (inactive)'
                    END
       		       )
       		  from cl_tx_users as users 
       		 inner join cl_tx_usr_roles as user_role
       		    on user_role.user_id = users.usr_id 
       		   and (
       		   			cardinality(selected_authorities_) = 0
       		   			or user_role.role_id = any(selected_authorities_)
       		   	   )
       		 where users.org_id = org_id_
        ));

		-- Get matching service request IDs based on filters
		matching_ids_json = array_to_json(array(
			select tms_get_srvc_reqs_by_filter(requester_info,1 , 100000, filter_, '')
		));

		SELECT array_agg((value->>'id')::int) 
		  FROM json_array_elements(matching_ids_json) AS value
		  into matching_ids;

		-- Get all possible statuses for the service type
		all_statuses = array_to_json(array(
			select jsonb_build_object(
				'status_key', srvc_statuses.status_key,
				'status_title', srvc_statuses.title
			)
			from cl_cf_srvc_statuses as srvc_statuses 
			where srvc_statuses.srvc_id = srvc_type_id_
			order by srvc_statuses.db_id
		));

		-- Build dynamic columns for each status
		_dynamic_columns = '';
		
		FOR _single_status_key in select json_array_elements(all_statuses)->>'status_key' loop
			_single_status_name = (
				select json_array_elements(all_statuses)->>'status_title'
				from json_array_elements(all_statuses)
				where json_array_elements(all_statuses)->>'status_key' = _single_status_key
				limit 1
			);
			
			_single_status_extracter = '( 
											select DATE(trnstn_log.trnstn_date) 
											  from cl_tx_srvc_req_trnstn_log as trnstn_log 
										     where trnstn_log.srvc_req_id = srvc_req.db_id
										       and trnstn_log.status_key = $$'|| _single_status_key || '$$ 
										     order by trnstn_log.trnstn_date desc
										     limit 1
										  )';
			
			_dynamic_columns = _dynamic_columns || ', ' || _single_status_extracter  || ' as "' ||  _single_status_name || '" '; 
		   
	    END LOOP;
	   

	   _dynamic_sql = 'CREATE MATERIALIZED VIEW '||materialized_table_name_||' AS 				  
								select 
									(
										select key
		                             from jsonb_each($$'||all_verticals||'$$)
		                            where srvc_req.srvc_type_id = ANY (array(select jsonb_array_elements(value))::int[])
                                    ) as "Vertical Name",
								   srvc_req.display_code as "Service Display Code"
								   ' || _dynamic_columns || '  
							      from public.cl_tx_srvc_req as srvc_req 
							     inner join cl_cf_service_types as srvc_type
						            on srvc_type.service_type_id = srvc_req.srvc_type_id 
						         inner join cl_tx_orgs as org
						            on srvc_req.org_id = org.org_id 
						         inner join cl_cf_srvc_statuses as srvc_status
						            on srvc_status.srvc_id = srvc_req.srvc_type_id 
						           and srvc_status.status_key = srvc_req.status 
						         inner join cl_tx_users c_by_usr 
						            on c_by_usr.usr_id = srvc_req.c_by
								 where srvc_req.db_id in (' || array_to_string(matching_ids,',') || ')
								 group by srvc_req.db_id, srvc_type.service_type_id, org.org_id, c_by_usr.usr_id, srvc_status.db_id
								 order by srvc_req.db_id desc
					';
 	
		raise notice 'vertical status transition sql %', _dynamic_sql;
		
		execute _dynamic_sql;
		
		status = true;
		message = 'success';
		resp_data = json_build_object('materialized_table_name', materialized_table_name_);
		
		return json_build_object('status',status,'code',message,'data',resp_data);
	END;
$function$
;
