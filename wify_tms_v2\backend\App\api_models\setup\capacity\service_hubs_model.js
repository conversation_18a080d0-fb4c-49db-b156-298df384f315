var sampleOperationResp = require('../../utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('../../utils/db_resp');
const users_model = require('../../users_model');
const geoCodingUtils = require('../../utils/geo_coding_utils');
class service_hubs_model {
    getSingleEntryData(query, entry_id) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                var form_data = JSON.stringify(query);
                let respData = (
                    await this.db.tms_hub_getview_data(form_data, entry_id)
                )[0].tms_hub_getview_data;

                if (!respData.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            respData?.message || 'Internal server error',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(respData.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getLocationInfo(query, pincodes) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                var form_data = query;

                let updatedPincodes = pincodes.map(
                    (pincode) => pincode + '+India'
                );
                form_data['pincodes'] = updatedPincodes;
                var temp_form_data = form_data;
                form_data = JSON.stringify(form_data);
                // console.log('getLocationInfo form_data send => ', form_data);

                // get exsting pincodes location details
                const dbResp = (
                    await this.db.tms_get_pincodes_geo_loc_from_cache(form_data)
                )[0].tms_get_pincodes_geo_loc_from_cache;

                // console.log('getLocationInfo form_data respData => ', dbResp);

                let locationDetails = [];
                let foundPincodes = new Set();

                if (dbResp && dbResp.status && dbResp.data) {
                    locationDetails = dbResp.data;
                    foundPincodes = new Set(
                        locationDetails.map((loc) => loc.address.split('+')[0])
                    ); // Extracting pincode from address
                }

                // Identify missing pincodes get loc details & save into db
                let missingPincodes = pincodes.filter(
                    (pincode) => !foundPincodes.has(pincode)
                );
                if (missingPincodes.length > 0) {
                    // Fetch locations for missing pincodes
                    let newLocationData =
                        await geoCodingUtils.fetchAndStorePincodeBoundaries(
                            missingPincodes
                        );
                    if (newLocationData) {
                        temp_form_data['location_data'] = newLocationData;
                        temp_form_data = JSON.stringify(temp_form_data);
                        let storeResp = (
                            await this.db.tms_add_geo_coding_cache_batch(
                                temp_form_data
                            )
                        )[0].tms_add_geo_coding_cache_batch;
                        if (storeResp.status) {
                            // Merge API-fetched data with existing DB data & send complete list
                            locationDetails = [
                                ...locationDetails,
                                ...newLocationData,
                            ];
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(locationDetails),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    dbResp?.message || 'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                        }
                    } else {
                        resolve(
                            new sampleOperationResp(
                                false,
                                `No location details for ${missingPincodes.join(', ')} pincodes`,
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                    }
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(dbResp.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                console.error('service_hubs_model::getLocationInfo', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    createOrUpdateHub(query, entry_id = 0) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                var form_data = JSON.stringify(query);
                let respData = (
                    await this.db.tms_create_or_update_srvc_hub(
                        form_data,
                        entry_id
                    )
                )[0].tms_create_or_update_srvc_hub;

                if (respData.code.includes('hub_name_already_exists')) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Hub name already exists',
                            HttpStatus.StatusCodes.CONFLICT
                        )
                    );
                } else if (respData.code.includes('hub_code_already_exists')) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Hub code already exists',
                            HttpStatus.StatusCodes.CONFLICT
                        )
                    );
                } else if (
                    respData.code.includes(
                        'pincode_already_exists_in_another_hub'
                    )
                ) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            respData.code || 'Internal server Error',
                            HttpStatus.StatusCodes.CONFLICT
                        )
                    );
                } else if (!respData.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            respData?.message || 'Internal server Error',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );

                    return;
                } else {
                    // Process service hub updates for service requests
                    this.processSrvcHubUpdateForServiceRequests(
                        query,
                        respData.data,
                        entry_id
                    );

                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(respData.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                // console.error('service_hubs_model::createOrUpdateHub', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    createOrUpdateBatch(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                var form_data = JSON.stringify(query);
                let respData = (
                    await this.db.tms_create_srvc_hub_batch(form_data)
                )[0].tms_create_srvc_hub_batch;

                if (!respData.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            respData?.message || 'Internal server Error',
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                } else {
                    // Process service hub updates for service requests for each created hub
                    this.processBulkSrvcHubUpdateForServiceRequests(
                        query,
                        respData.data
                    );

                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(respData.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                // console.error('service_hubs_model::createOrUpdateBatch', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error,
                        HttpStatus.StatusCodes.BAD_REQUEST
                    )
                );
            }
        });
    }

    getAllHubs(form, vertical_id) {
        return new Promise(async (resolve, reject) => {
            try {
                if (Object.keys(form).length != 0) {
                    var org_id = users_model.getOrgId(this.userContext);
                    var pagination = JSON.parse(form.pagination);
                    var page_no = pagination.current;
                    var page_size = pagination.pageSize;
                    var search_query = form.search_query;
                    //Filters remove -1 in from array
                    var filters = JSON.parse(form.filters);
                    var filtersAllKeys = Object.keys(filters);
                    for (var i = 0; i < filtersAllKeys.length; i++) {
                        if (
                            filters[filtersAllKeys[i]]?.length > 0 &&
                            Array.isArray(filters[filtersAllKeys[i]]) &&
                            filters[filtersAllKeys[i]].includes('-1')
                        ) {
                            delete filters[filtersAllKeys[i]];
                        }
                    }
                    filters = JSON.stringify(filters);
                }
                const dbResp = (
                    await this.db.tms_get_srvc_hubs(
                        org_id,
                        vertical_id,
                        page_no,
                        page_size,
                        filters,
                        search_query
                    )
                )[0].tms_get_srvc_hubs;
                // console.log('dbResp tms_get_srvc_hubs => dbResp');
                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            dbResp?.message || 'Internal server Error',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(dbResp.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getViewDataFrHubForm(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                let respData = (
                    await this.db.tms_hub_getview_data(JSON.stringify(query))
                )[0].tms_hub_getview_data;
                if (!respData.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            respData?.message || 'Unable to load',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(respData.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                // console.error('service_hubs_model::getProto', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Unable to load',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getOverviewProto(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                let respData = (
                    await this.db.tms_get_service_hubs_overview_proto(
                        JSON.stringify(query)
                    )
                )[0].tms_get_service_hubs_overview_proto;
                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(respData.data),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error('service_hubs_model::getProto', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Unable to load',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    getInstance() {
        const instance = new service_hubs_model();
        return instance;
    }

    getFreshInstance(model) {
        const clonedInstance = new service_hubs_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }

    /**
     * Process service hub updates for service requests
     * This function will be called after a service hub is created or updated
     * to handle any necessary updates to related service requests
     * @param {Object} query - The query object containing form data
     * @param {Object} responseData - The response data from hub creation/update
     */
    processSrvcHubUpdateForServiceRequests(
        query,
        responseData,
        update_entry_id
    ) {
        try {
            console.log(
                'service_hubs_model::processSrvcHubUpdateForServiceRequests:: Starting processing for service hub updates'
            );

            const { allQueues } = require('../../queues_v2/queues');
            const org_id = users_model.getOrgId(this.userContext);
            const usr_id = users_model.getUUID(this.userContext);
            const entry_id = responseData?.entry_id;

            if (!entry_id && !update_entry_id) {
                console.log(
                    'service_hubs_model::processSrvcHubUpdateForServiceRequests:: No entry_id found, skipping processing'
                );
                return;
            }

            // Determine if this is a new hub or existing hub update
            const isNewHub = update_entry_id > 0 ? false : true;

            if (isNewHub) {
                // New Hub added: Loop on all service requests that fall under the vertical
                // and have no hub assigned and have the pincode inside that hub
                console.log(
                    `service_hubs_model::processSrvcHubUpdateForServiceRequests:: Processing new hub creation for entry_id: ${entry_id}`
                );

                const jobData = {
                    query: {
                        org_id,
                        usr_id,
                        hub_id: entry_id,
                        ip_address: this.ip_address,
                        user_agent: this.user_agent_,
                    },
                    operation: 'assign_new_hub',
                    service_hubs_model_data: this.getServiceHubsModelData(this),
                };

                allQueues.WIFY_UPDATE_SRVC_HUB_FOR_SRVC_REQS.addJob(jobData);

                console.log(
                    `service_hubs_model::processSrvcHubUpdateForServiceRequests:: Added job to assign new hub ${entry_id} to service requests`
                );
            } else {
                // Existing hub modified: Update service requests that are tagged to this hub
                // and if the new hub definition does not match or the hub is deactivated then untag the hub
                console.log(
                    `service_hubs_model::processSrvcHubUpdateForServiceRequests:: Processing existing hub update for entry_id: ${entry_id}`
                );

                const jobData = {
                    query: {
                        org_id,
                        usr_id,
                        hub_id: entry_id,
                        ip_address: this.ip_address,
                        user_agent: this.user_agent_,
                    },
                    operation: 'update_existing_hub',
                    service_hubs_model_data: this.getServiceHubsModelData(this),
                };

                allQueues.WIFY_UPDATE_SRVC_HUB_FOR_SRVC_REQS.addJob(jobData);

                console.log(
                    `service_hubs_model::processSrvcHubUpdateForServiceRequests:: Added job to update existing hub ${entry_id} assignments`
                );
            }

            console.log(
                'service_hubs_model::processSrvcHubUpdateForServiceRequests:: Processing completed'
            );
        } catch (error) {
            console.error(
                'service_hubs_model::processSrvcHubUpdateForServiceRequests:: Error processing service hub updates for service requests:',
                error
            );
        }
    }

    /**
     * Process service hub updates for service requests for bulk creation
     * This function will be called after bulk service hubs are created
     * to handle any necessary updates to related service requests for each created hub
     * @param {Object} query - The query object containing form data
     * @param {Object} responseData - The response data from bulk hub creation containing entry_ids array
     */
    processBulkSrvcHubUpdateForServiceRequests(query, responseData) {
        try {
            const entry_ids = responseData?.entry_ids;
            if (
                !entry_ids ||
                !Array.isArray(entry_ids) ||
                entry_ids.length === 0
            ) {
                console.log(
                    'service_hubs_model::processBulkSrvcHubUpdateForServiceRequests:: No entry_ids found, skipping processing'
                );
                return;
            }

            console.log(
                `service_hubs_model::processBulkSrvcHubUpdateForServiceRequests:: New Hubs Count = ${entry_ids.length}`
            );

            // Since these are all new hubs (bulk creation), we need to process each one
            const batch_data = query.batch_data;
            if (!batch_data || !Array.isArray(batch_data)) {
                console.log(
                    'service_hubs_model::processBulkSrvcHubUpdateForServiceRequests:: No batch_data found, skipping processing'
                );
                return;
            } else if (batch_data.length !== entry_ids.length) {
                console.log(
                    'service_hubs_model::processBulkSrvcHubUpdateForServiceRequests:: Seems like some hubs failed to create, skipping processing'
                );
                return;
            }

            // Process each created hub
            entry_ids.forEach((entry_id, index) => {
                if (index < batch_data.length) {
                    const hubData = batch_data[index];
                    // Convert pin_codes string to array format for processing
                    let pincodes = [];
                    if (hubData.pin_codes) {
                        pincodes = hubData.pin_codes
                            .split(',')
                            .map((code) => code.trim())
                            .filter((code) => code);
                    }

                    const singleHubQuery = {
                        vertical_id: query.vertical_id,
                        pin_codes: pincodes,
                        is_active: true,
                    };

                    const singleHubRespData = {
                        entry_id: entry_id,
                    };

                    console.log(
                        `service_hubs_model::processBulkSrvcHubUpdateForServiceRequests:: Process Hub =  ${entry_id} (${hubData.hub_name})`
                    );

                    this.processSrvcHubUpdateForServiceRequests(
                        singleHubQuery,
                        singleHubRespData,
                        0 // entry_id = 0 indicates new hub creation
                    );
                    console.log();
                }
            });

            console.log(
                `service_hubs_model::processBulkSrvcHubUpdateForServiceRequests:: Total ${entry_ids.length} Hubs of Vertical = ${query.vertical_id}, Added for Processing`
            );
        } catch (error) {
            console.error(
                'service_hubs_model::processBulkSrvcHubUpdateForServiceRequests:: Error processing bulk service hub updates for service requests:',
                error
            );
        }
    }

    /**
     * Get model data for queue
     * @returns {Object} Model data for queue
     */
    getServiceHubsModelData(model) {
        return {
            ip_address: model.ip_address,
            user_agent: model.user_agent_,
            userContext: model.userContext,
        };
    }

    /**
     * Assign new hub to service requests
     * For new hubs: Loop on all service requests that fall under the vertical
     * and have no hub assigned and have the pincode inside that hub
     * @param {Object} query - The query object containing form data
     */
    async assignNewHubToServiceRequests(query) {
        try {
            console.log(
                'service_hubs_model::assignNewHubToServiceRequests::',
                query
            );

            // Add limit to query for batch processing
            const queryWithLimit = {
                ...query,
                limit: 100,
            };

            // Reuse the existing hub update function since new hub assignment
            // is essentially an update operation with no existing assignments to untag
            const result =
                await this.db.tms_ace_workflow_sync_srvc_reqs_for_hub_create_or_update(
                    JSON.stringify(queryWithLimit)
                );

            const dbResp =
                result[0]
                    .tms_ace_workflow_sync_srvc_reqs_for_hub_create_or_update;

            if (dbResp.status) {
                const data = dbResp.data || {};
                console.log(
                    'service_hubs_model::assignNewHubToServiceRequests:: data',
                    JSON.stringify(data)
                );
                console.log(
                    `service_hubs_model::assignNewHubToServiceRequests:: Successfully assigned hub_id: ${query.hub_id}:`,
                    `total_updated: ${data.total_updated_count || 0},`,
                    `tagged: ${data.tagged_count || 0}`
                );

                // Check if we need to process more records
                if (data.total_updated_count === data.limit_used) {
                    console.log(
                        `service_hubs_model::assignNewHubToServiceRequests:: Limit reached (${data.limit_used}), adding same job for continued processing`
                    );

                    const { allQueues } = require('../../queues_v2/queues');
                    const jobData = {
                        query: query,
                        operation: 'assign_new_hub',
                        service_hubs_model_data:
                            this.getServiceHubsModelData(this),
                    };

                    allQueues.WIFY_UPDATE_SRVC_HUB_FOR_SRVC_REQS.addJob(
                        jobData
                    );

                    console.log(
                        `service_hubs_model::assignNewHubToServiceRequests:: Added continuation job for hub_id: ${query.hub_id}`
                    );
                } else {
                    console.log(
                        'service_hubs_model::assignNewHubToServiceRequests:: All Batch Processing completed'
                    );
                }
            } else {
                console.error(
                    'service_hubs_model::assignNewHubToServiceRequests:: Error:',
                    dbResp.message
                );
            }
        } catch (error) {
            console.error(
                'service_hubs_model::assignNewHubToServiceRequests:: Error:',
                error
            );
        }
    }

    /**
     * Update existing hub for service requests
     * For existing hubs: Update service requests that are tagged to this hub
     * and if the new hub definition does not match or the hub is deactivated then untag the hub
     * @param {Object} query - The query object containing form data
     */
    async updateExistingHubForServiceRequests(query) {
        try {
            console.log(
                'service_hubs_model::updateExistingHubForServiceRequests::',
                query
            );

            // Add limit to query for batch processing
            const queryWithLimit = {
                ...query,
                limit: 100,
            };

            const result =
                await this.db.tms_ace_workflow_sync_srvc_reqs_for_hub_create_or_update(
                    JSON.stringify(queryWithLimit)
                );

            const dbResp =
                result[0]
                    .tms_ace_workflow_sync_srvc_reqs_for_hub_create_or_update;

            if (dbResp.status) {
                const data = dbResp.data || {};
                console.log(
                    'service_hubs_model::updateExistingHubForServiceRequests:: data',
                    JSON.stringify(data)
                );
                console.log(
                    `service_hubs_model::updateExistingHubForServiceRequests:: Successfully updated hub_id: ${query.hub_id}:`,
                    `total_updated: ${data.total_updated_count || 0},`,
                    `untagged: ${data.untagged_count || 0},`,
                    `tagged: ${data.tagged_count || 0}`
                );

                // Check if we need to process more records
                if (data.total_updated_count === data.limit_used) {
                    console.log(
                        `service_hubs_model::updateExistingHubForServiceRequests:: Limit reached (${data.limit_used}), adding same job for continued processing`
                    );

                    const { allQueues } = require('../../queues_v2/queues');
                    const jobData = {
                        query: query,
                        operation: 'update_existing_hub',
                        service_hubs_model_data:
                            this.getServiceHubsModelData(this),
                    };

                    allQueues.WIFY_UPDATE_SRVC_HUB_FOR_SRVC_REQS.addJob(
                        jobData
                    );

                    console.log(
                        `service_hubs_model::updateExistingHubForServiceRequests:: Added continuation job for hub_id: ${query.hub_id}`
                    );
                } else {
                    console.log(
                        'service_hubs_model::updateExistingHubForServiceRequests:: All Batch Processing completed'
                    );
                }
            } else {
                console.error(
                    'service_hubs_model::updateExistingHubForServiceRequests:: Error:',
                    dbResp.message
                );
            }
        } catch (error) {
            console.error(
                'service_hubs_model::updateExistingHubForServiceRequests:: Error:',
                error
            );
        }
    }
}

module.exports = new service_hubs_model();
