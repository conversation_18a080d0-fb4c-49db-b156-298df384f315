const AWS = require('aws-sdk');
const { PassThrough } = require('stream');
const s3 = new AWS.S3({
    accessKeyId: process.env.S3_ACCESS_KEY,
    secretAccessKey: process.env.S3_SECRET_KEY,
    region: process.env.S3_REGION,
});

async function getSignedUrl({ s3Key }) {
    const params = {
        Bucket: process.env.S3_BUCKET,
        Key: s3Key,
        Expires: 600,
    };
    return s3.getSignedUrlPromise('getObject', params);
}

function uploadStreamToS3({ bucketName, s3Key, contentType }) {
    const passThrough = new PassThrough();
    const params = {
        Bucket: bucketName,
        Key: s3Key,
        Body: passThrough,
        ContentType: contentType,
    };

    const uploadPromise = s3.upload(params).promise();
    return { writeStream: passThrough, uploadPromise };
}

module.exports = { getSignedUrl, uploadStreamToS3, s3 };
