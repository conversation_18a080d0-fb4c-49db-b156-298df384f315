CREATE OR REPLACE FUNCTION public.tms_get_srvc_req_dumps_fr_usr(requester_info json, filter_ json)
 RETURNS SETOF json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
-- 	Bare minimums
	status boolean;
	message text;
	resp_data json;
	ip_address_ text;
	user_agent_ text;
	org_id_ integer;
	usr_id_ uuid;
--  Specific
	srvc_type_id_ integer;	
-- 	Filters
	filter_statuses text[];

	
--  Temps
	matching_ids_json json;
	matching_ids bigint[];
	temp_id bigint;
	_dynamic_columns text;
	_single_col_key text;
	_single_col_name text;
	_single_col_extracter text;
	field_label_mapping json;
	custom_fields_json json;
	feedback_fields_json json;
    _single_col_details json;
    _date_field_match_result text;
    is_feedback_field boolean;
    gai_rating_join text default '';
	srvc_open_status_trxn_join text default '';
    onfield_subtask_types integer[];
    is_onfield_task boolean;
    onfield_task_json json;
    _temp_custom_select_field_details text;
    is_txn_overview boolean;
	_single_col_details_fr_txn text;
	sp_custom_fields_json json;
    is_sp_custom_field boolean;
    custom_field_column text;
    --locked_for_change
    locked_for_change_json json;
   	locked_for_change_column text;
   	is_locked_for_change bool;
    is_sp_locked_for_change bool;
    is_sp_locked_for_change_by bool;
    is_sp_locking_date bool;
    is_org_info bool;
    org_info json;
   	filters_json json;
    verticals_list_ integer[];
    all_verticals json;
    authorities_ json;
    selected_authorities_ int[] default '{}';
    user_list json; 
    prefix_authorities_key text;
    is_srvc_prvdr bool default false;
    --export
   	calculated_task_json json;
    line_item_json json;
    pi_info_json json;
    payout_info_json json;
   _single_col_details_fr_sp_attachment text;
   _single_col_details_fr_sp_auth text;
   _single_col_details_fr_brand_auth text;
    location_group_json json;
    loc_groups_data json;
   	gai_info_json json;
   	is_calculated_taske bool;
    is_line_item bool;
    is_pi_info bool;
   	is_sp_authorities bool;
    is_brand_authorities bool; 
   is_location_group bool;
  	is_gai_info_ bool;
  	is_sp_attachment bool;
  	is_payout_info bool;
  	additional_order_by text default '';
  	additional_group_by text default '';
	is_filter_sorting bool default false;
    filter_sort_by text;
    filter_sort_order text;
    vertical_list_join text default '';
	p_and_l_data_json json;
-- 	Output
	_dynamic_sql text;

	begin
		status = false;
		message = 'Internal_error';
	
		org_id_ = json_extract_path_text(requester_info,'org_id');
		usr_id_ = json_extract_path_text(requester_info,'usr_id');
		ip_address_ = json_extract_path_text(requester_info,'ip_address');
		user_agent_ = json_extract_path_text(requester_info,'user_agent');
		srvc_type_id_ = json_extract_path_text(requester_info,'srvc_type_id');
		field_label_mapping = requester_info->'field_label_mapping';
		filters_json = json_extract_path_text(requester_info,'filters');
		verticals_list_ = array( select json_array_elements_text(json_extract_path(filters_json,'verticals_list')) )::integer[];
		
		filter_statuses = array( select json_array_elements_text(json_extract_path(filter_,'statuses')) )::text[];
		is_srvc_prvdr = tms_hlpr_is_org_srvc_prvdr(org_id_);
		onfield_subtask_types = array(
			select sbtsk_type.sbtsk_type_id 
			  from cl_cf_sbtsk_types as sbtsk_type
			 where (sbtsk_type.form_data->>'sbtsk_is_onfield')::bool is true
		);
--		raise notice 'onfield_subtask_types %', onfield_subtask_types;
		
--		raise notice 'Filter filter_statuses %',json_extract_path(filter_,'filter_statuses');
	-- get the all possible location group for organisation 
       loc_groups_data = array_to_json(array(
       		select jsonb_build_object(
       					'id',id,
       					'groups_name',groups_name,
       					'group_data',tms_get_city_state_ex_city_from_grps(array[id])
       		       )
       		  from cl_tx_location_groups 
       		 where org_id = org_id_
       ));
	--vertical 
	    all_verticals = (
				   select jsonb_object_agg(
						settings_data->>'vertical_title',
						settings_data->'srvc_type_id'
				    )
				 	from cl_tx_orgs_settings as org_vertical
				   where settings_type = 'SP_CUSTOM_FIELDS'
				     and org_id = org_id_
				     and settings_data->>'srvc_type_id' is not null
				     and settings_data->>'vertical_title' <> ''
		);
	
		prefix_authorities_key = 'brand_';
	    if tms_hlpr_is_org_srvc_prvdr(org_id_) then
	    	prefix_authorities_key = 'sp_';
	    end if;
	   
		authorities_ = requester_info->>(prefix_authorities_key || 'authorities');
		if authorities_ is not null then
				SELECT ARRAY(
					    SELECT CAST(regexp_replace(authorities_role, '[^0-9]', '', 'g') AS integer)
		          	     FROM json_array_elements_text(authorities_) AS authorities_role 
		          	   )
	        	  into selected_authorities_;
		end if;
	
		user_list = array_to_json(array(
       		select jsonb_build_object(
   					'id',users.usr_id,
   					'name',
					CASE 
                        WHEN users.is_active THEN users."name"
                        ELSE users."name" || ' (inactive)'
                    END
       		       )
       		  from cl_tx_users as users 
       		 inner join cl_tx_usr_roles as user_role
       		    on user_role.user_id = users.usr_id 
       		   and (
       		   			cardinality(selected_authorities_) = 0
       		   			or user_role.role_id = any(selected_authorities_)
       		   	   )
       		 where users.org_id = org_id_
       	    -- and users.is_active is not false --(we want to show deactivated user name in srvc req dump)
        ));
        --sort order by section start
		filter_sort_by   = filter_->>'sort_by'; 
		filter_sort_order   = filter_->>'sorting'; 
		additional_order_by = 'srvc_req.db_id desc';
		if filter_sort_by is not null and filter_sort_order is not null then	
			is_filter_sorting = true;
		end if;
		if is_filter_sorting is true then	
	
			if filter_sort_by = 'creation_date' then 
				srvc_open_status_trxn_join = ' inner join cl_tx_srvc_req_trnstn_log as trnstn_log
										         on trnstn_log.srvc_req_id = srvc_req.db_id
										        and trnstn_log.status_key = ' || quote_literal('open') ||' ';
				additional_order_by = ' trnstn_log.trnstn_date '|| filter_sort_order ||' NULLS LAST ';
				additional_group_by = ', trnstn_log.trnstn_date';
			
			elsif filter_sort_by = 'req_srvc_date' then
				additional_order_by = ' srvc_req.form_data->>$$request_req_date$$ '|| filter_sort_order ||' NULLS LAST ';
			
			elsif filter_sort_by = 'creation_srvc_req_date' then
				additional_order_by = ' (srvc_req.c_meta).time '|| filter_sort_order ||' NULLS LAST ';
			
			elsif filter_sort_by = 'statuses' then
				additional_order_by = ' srvc_req.status '|| filter_sort_order ||' ';
			
			elsif filter_sort_by = 'priority' then
				additional_order_by = ' srvc_req.priority '|| filter_sort_order ||' ';
			
			elsif filter_sort_by = 'assgn_to_prvdr_date' then
				additional_order_by = ' srvc_req.srvc_prvdr_assg_time '|| filter_sort_order ||' NULLS LAST ';
			
			elsif filter_sort_by = 'srvc_status_title' then
				additional_order_by = ' srvc_status.title '|| filter_sort_order ||' ';
			
			elsif filter_sort_by = 'srvc_org' then
				additional_order_by = ' org.nickname '|| filter_sort_order ||' ';
			
			elsif filter_sort_by = 'sp_first_task_date' then
				additional_order_by = ' first_sp_sbtsk.start_time '|| filter_sort_order ||' NULLS LAST ';
			
			elsif filter_sort_by = 'sp_last_task_date' then
				additional_order_by = ' last_sp_sbtsk.start_time '|| filter_sort_order ||' NULLS LAST ';
			
			elsif filter_sort_by = 'cust_city' then
				additional_order_by = ' srvc_req.form_data->>$$cust_city$$ '|| filter_sort_order ||' NULLS LAST ';
			
			elsif filter_sort_by = 'cust_state' then
				additional_order_by = ' srvc_req.form_data->>$$cust_state$$ '|| filter_sort_order ||' NULLS LAST ';
	
			elsif filter_sort_by = 'srvc_req_created_by' then
				additional_order_by = ' c_by_usr.name '|| filter_sort_order ||' NULLS LAST ';
			
			elsif filter_sort_by = 'verticals_list' then
				vertical_list_join = ' left join jsonb_each($$'||all_verticals||'$$::jsonb) as org_settings_
                                     on srvc_req.srvc_type_id = ANY (array(select jsonb_array_elements(value))::int[])';
				additional_order_by = ' org_settings_.key '|| filter_sort_order ||' NULLS LAST ';
				additional_group_by = ', org_settings_.key';

			end if;
		
		end if;
	--    raise notice 'all_verticals %',all_verticals;
	--   raise notice 'loc_groups_data %',loc_groups_data;
		matching_ids_json = array_to_json(array(
			select tms_get_srvc_reqs_by_filter(requester_info,1 , 100000, filter_, '')
		));
	
		SELECT array_agg((value->>'id')::int) 
		  FROM json_array_elements(matching_ids_json) AS value
		  into matching_ids;
	
		 -- json_array_elements
--		FOR single_id_index IN 0..json_array_length(matching_ids_json) - 1 loop
--			temp_id = json_extract_path_text(matching_ids_json -> single_id_index,'id');
--	      	matching_ids = array_append(matching_ids, temp_id);
--	    END LOOP;
		-- raise notice 'Matching ids %', matching_ids;

--		return query SELECT jsonb_build_object('nickname',nickname) FROM cl_tx_orgs;
		_dynamic_columns = '';
		
			select case 
				   	when length(srvc_type.form_data->>'srvc_cust_fields_json') > 0 
				   	then ((srvc_type.form_data#>>'{srvc_cust_fields_json}')::jsonb#>>'{translatedFields}')::jsonb
				   end
			  from cl_cf_service_types as srvc_type 
			 where service_type_id = srvc_type_id_ 
			  into custom_fields_json;
		
		 
		
		 select ((srvc_type.form_data#>>'{srvc_rate_fields_json}')::jsonb#>>'{translatedFields}')::jsonb
		   from cl_cf_service_types as srvc_type 
		  where srvc_type.form_data#>>'{srvc_rate_fields_json}' <> '' and service_type_id = srvc_type_id_ 
		   into feedback_fields_json;
		  
		IF feedback_fields_json IS NULL THEN
			  feedback_fields_json = '[{"key":"feedback_received","label":"Feedback Received(Yes/No)"}, {"key":"cust_rating","label":"Rating"}]'::jsonb;
		  ELSE
			  feedback_fields_json = feedback_fields_json::jsonb || '{"key":"feedback_received","label":"Feedback Received(Yes/No)"}'::jsonb || '{"key":"cust_rating","label":"Rating"}'::jsonb;
		  END IF;

		  p_and_l_data_json = '[{"key":"gm_per", "label":"GM%"},
                      {"key":"net_gm", "label":"Net GM"},
                      {"key":"total_revenue", "label":"Total Revenue"},
                      {"key":"total_cost", "label":"Total Cost"}]';

		  
		  onfield_task_json = '[{"key":"assignee_name", "label":"Assignee name"},
								{"key":"task_status", "label":"Task status"},
								{"key":"task_start_date", "label":"Task start date", "widget":"date-picker"},
								{"key":"assigned_by", "label":"Assigned by"},
							    {"key":"assigned_date", "label":"Assigned date","widget":"date-picker"},
								{"key":"assigned_time", "label":"Assigned time"},
								{"key":"task_status_remark", "label":"Task status remark"},
                                {"key":"assignee_phone_no.", "label":"Assignee Phone No."},
								{"key":"sp_first_task_date", "label":"SP First Task Date","widget":"date-picker"},
                                {"key":"sp_last_task_date", "label":"SP Last Task Date","widget":"date-picker"},
								{"key":"gai_remarks", "label":"GAI remarks"}]';
									
		 locked_for_change_json = '[{"key":"locked_for_change", "label":"Locked for change (Yes/No)"},
									{"key":"sp_locked_for_change", "label":"Locked for change (Yes/No)"},
									{"key":"sp_locked_for_change_by", "label":"SP Locked for change by"},
									{"key":"sp_locking_date", "label":"SP Locking Date","widget":"date-picker"}]';
																
		 --org info
		 org_info = '[{"key":"brand_name", "label":"Brand Name"},
					  {"key":"service_type", "label":"Service Type"},
					  {"key":"vertical_fr_req_dump", "label":"Vertical"}]';	
					 
		 calculated_task_json = '[{"key":"mandays", "label":"Mandays"},
								  {"key":"sp_work_progress", "label":"SP Work progress"}]';			
		 line_item_json 	  = '[{"key":"sp_total_quantity", "label": "SP Total Quantity"},
								  {"key":"sp_total_price", "label": "SP Total Price"}]';
								 
		 pi_info_json 		  = '[{"key":"final_amount", "label":"final_amount"},
								  {"key":"sp_sent_for_billing", "label":"SP sent for billing"}]';
		 
		 location_group_json = '[{"key":"location_group", "label":"Location Group"}]';
		
		 gai_info_json = '[{"key":"avg_gai_rating", "label":"Avg. GAI rating"},
						   {"key":"no_of_gai_rated_tasks", "label":"No. of GAI rated tasks"}]';
						  
		 payout_info_json 		  = '[{"key":"total_quantity", "label":"Total quantity"},
								      {"key":"total_price", "label":"Total price"},
									  {"key":"total_payout", "label":"Total"}]';
		if cardinality(verticals_list_) > 0 then
			 select ((sp_custom_fields.settings_data#>>'{sp_cust_fields_json}')::jsonb#>>'{translatedFields}')::jsonb
			   from cl_tx_orgs_settings as sp_custom_fields 
			  where sp_custom_fields.db_id = verticals_list_[1]
			   into sp_custom_fields_json;
		else
		--service provider custom fields without SP_field_id
			 select ((sp_custom_fields.settings_data#>>'{sp_cust_fields_json}')::jsonb#>>'{translatedFields}')::jsonb
			   from cl_tx_orgs_settings as sp_custom_fields 
			  where sp_custom_fields.org_id = org_id_
			    and sp_custom_fields.settings_type = 'SP_CUSTOM_FIELDS'
			    and srvc_type_id_ = any(array(SELECT json_array_elements_text(json_extract_path(sp_custom_fields.settings_data,'srvc_type_id')))::integer[])
			   into sp_custom_fields_json;
		end if;
			  
		   
--		 raise notice 'sp fields %',sp_custom_fields_json;
--		 raise notice 'feedback_fields_json %',feedback_fields_json;
--		 raise notice 'custom_fields_json %', custom_fields_json;
--		 raise notice 'onfield_task_json %', onfield_task_json;
		 
		 FOR _single_col_key in select json_array_elements_text(requester_info->'selected_columns') loop
	       	_single_col_name = field_label_mapping->>_single_col_key;
	        _single_col_extracter = ' srvc_req.form_data->>$$' || _single_col_key || '$$ ';
	       
	         is_feedback_field =  false;
	         is_onfield_task = false;
	         is_txn_overview = false;
	         is_sp_custom_field = false;
	         is_org_info = false;
	        
	         is_calculated_taske = false;
		     is_line_item = false;
		     is_pi_info = false;
		     is_location_group = false;		     
		     is_sp_authorities = false;
	         is_brand_authorities = false;
	         is_gai_info_ = false;
	        select value
			  from json_array_elements(custom_fields_json)
			 where _single_col_key = value->>'key'
			 limit 1
			  into _single_col_details;
			 
			
			 if _single_col_details is null then
			 	 -- checking for feedback fields
			 	select value
				  from json_array_elements(feedback_fields_json)
				 where _single_col_key = value->>'key'
				 limit 1
				  into _single_col_details;
				 
				if _single_col_details is not null then
					-- This field is from feedback
					is_feedback_field = true;
				end if;
			 end if;
			
			
			 if _single_col_details is null then
				--checking for onfield task
			 	select value from json_array_elements(onfield_task_json) 
			 	 where _single_col_key = value->>'key'
			 	 limit 1
			      into _single_col_details ;
				if _single_col_details is not null then
					-- This field is onfield task
					is_onfield_task = true;
				end if;
			 end if;
			
			if _single_col_details is null then
				--checking for org info
			 	select value from json_array_elements(org_info) 
			 	 where _single_col_key = value->>'key'
			 	 limit 1
			      into _single_col_details ;
				if _single_col_details is not null then
					-- This field is org info
					is_org_info = true;
				end if;
			 end if;
			
			
			if _single_col_details is null then
				--checking for transection overview
			 	select value from json_array_elements_text(requester_info->'transition_overview_fields') 
			 	 where _single_col_key = value
			 	 limit 1
			      into _single_col_details_fr_txn;
			   
			    if _single_col_details_fr_txn is not null then
					is_txn_overview = true;
				end if;
			end if;
		
			if _single_col_details is null then
				--checking for sp authority
			 	select value from json_array_elements_text(requester_info->'sp_authorities') 
			 	 where _single_col_key = value
			 	 limit 1
			      into _single_col_details_fr_sp_auth;
			   
			    if _single_col_details_fr_sp_auth is not null then
					is_sp_authorities = true;
				end if;
			end if;
			
			if _single_col_details is null then
			 	 -- checking custom fields for service provider 
			 	select value
				  from json_array_elements(sp_custom_fields_json)
				 where _single_col_key = value->>'key'
				 limit 1
				  into _single_col_details;
				 
				if _single_col_details is not null then
					is_sp_custom_field = true;
				end if;
			end if;
			
			if _single_col_details is null then
			    select value
			    from json_array_elements(p_and_l_data_json)
			    where _single_col_key = value->>'key'
			    limit 1
			    into _single_col_details;
			    
			    if _single_col_details is not null then
					_single_col_extracter = '
						CASE 
							WHEN trim(srvc_req.profit_and_loss_data->>$$' || _single_col_key || '$$) = ''NaN'' THEN ''0''
							ELSE srvc_req.profit_and_loss_data->>$$' || _single_col_key || '$$ 
						END
					';
			    end if;
			end if;
			
			if _single_col_details is null then
			 	select value from json_array_elements(locked_for_change_json) 
			 	 where _single_col_key = value->>'key'
			 	 limit 1
			      into _single_col_details;
			     
			     if _single_col_details is not null then
			     	if _single_col_details->>$$key$$ = 'locked_for_change' then
			     		is_locked_for_change = true;
			     	elseif _single_col_details->>$$key$$ = 'sp_locked_for_change' then
			     		is_sp_locked_for_change = true;
			     	elseif _single_col_details->>$$key$$ = 'sp_locked_for_change_by' then
			     		is_sp_locked_for_change_by = true;
					elseif _single_col_details->>$$key$$ = 'sp_locking_date' then
			     		is_sp_locking_date = true;
			     	end if;
			     end if;
			end if;	
		
			if _single_col_details is null then
			 	select value from json_array_elements(calculated_task_json) 
			 	 where _single_col_key = value->>'key'
			 	 limit 1
			      into _single_col_details;
			     
			     if _single_col_details is not null then
			     	if _single_col_details->>$$key$$ = 'mandays' or _single_col_details->>$$key$$ = 'sp_work_progress' then
			     		is_calculated_taske = true;
			     	end if;
			     end if;
			    
			end if;	
		
			if _single_col_details is null then
			 	select value from json_array_elements(line_item_json) 
			 	 where _single_col_key = value->>'key'
			 	 limit 1
			      into _single_col_details;
			     
			     if _single_col_details is not null then
			     	if _single_col_details->>$$key$$ = 'sp_total_quantity' or _single_col_details->>$$key$$ = 'sp_total_price' then
			     		is_line_item = true;
			     	end if;
			     end if;
			end if;	
		
			if _single_col_details is null then
			 	select value from json_array_elements(pi_info_json) 
			 	 where _single_col_key = value->>'key'
			 	 limit 1
			      into _single_col_details;
			     
			     if _single_col_details is not null then
			     	if _single_col_details->>$$key$$ = 'final_amount' or 
			     		_single_col_details->>$$key$$ = 'sp_sent_for_billing' then
			     		is_pi_info = true;
			     	end if;
			     end if;
			end if;
			
			if _single_col_details is null then
			 	select value from json_array_elements(payout_info_json) 
			 	 where _single_col_key = value->>'key'
			 	 limit 1
			      into _single_col_details;
			     
			     if _single_col_details is not null then
			     	if _single_col_details->>$$key$$ = 'total_quantity' or 
			     		_single_col_details->>$$key$$ = 'total_price' or 
			     		_single_col_details->>$$key$$ = 'total_payout' then
			     		is_payout_info = true;
			     	end if;
			     end if;
			end if;
		
			if is_sp_custom_field then
			 	select value from json_array_elements_text(requester_info->'sp_attachment_field_info')
			 	 where _single_col_key = value
			 	 limit 1
			      into _single_col_details_fr_sp_attachment;
				  
			    if _single_col_details_fr_sp_attachment is not null then
					is_sp_attachment = true;
				end if;
			end if;
			
			if _single_col_details is null then
			 	select value from json_array_elements(gai_info_json) 
			 	 where _single_col_key = value->>'key'
			 	 limit 1
			      into _single_col_details;
			     
			     if _single_col_details is not null then			     	
			     		is_gai_info_ = true;			     	
			     end if;
			     
			     if is_gai_info_ then
			     	if _single_col_details->>'key' = 'avg_gai_rating' then 		
			     	
						_single_col_extracter = 'avg((gai_rating_sbtsks.form_data->$$gai_rating$$->$$gemini$$->>$$rating$$)::real)';
				
					elsif  _single_col_details->>'key' = 'no_of_gai_rated_tasks' then 	
					
						_single_col_extracter = 'count(gai_rating_sbtsks.form_data->$$gai_rating$$->$$gemini$$->>$$rating$$)';
					
					end if;

					if is_srvc_prvdr and gai_rating_join = '' then
				    	gai_rating_join := gai_rating_join || 'left join cl_tx_sbtsk as gai_rating_sbtsks
												    on gai_rating_sbtsks.org_id = '|| org_id_ ||'
												   and gai_rating_sbtsks.srvc_req_id = srvc_req.db_id
												   and gai_rating_sbtsks.form_data->$$gai_rating$$->$$gemini$$->>$$rating$$ is not null';
				    end if;
			     end if;
			end if;	
		
		    if _single_col_details is null then
			 	select value from json_array_elements(location_group_json) 
			 	 where _single_col_key = value->>'key'
			 	 limit 1
			      into _single_col_details;
			     
			     if _single_col_details is not null then
			     	if _single_col_details->>$$key$$ = 'location_group' then
			     		is_location_group = true;
			     	end if;
			     end if;
			end if;	
			
			if _single_col_details is null then
				--checking for sp authority
			 	select value from json_array_elements_text(requester_info->'brand_authorities') 
			 	 where _single_col_key = value
			 	 limit 1
			      into _single_col_details_fr_brand_auth;
			   
			    if _single_col_details_fr_brand_auth is not null then
					is_brand_authorities = true;
				end if;
			end if;
		
		    select value_ from  unnest('{request_req_date,creation_date}'::text[]) as value_
			 where _single_col_key = value_
		      into _date_field_match_result;

			
			
			if is_feedback_field then 
				if _single_col_details->>'key' = 'cust_rating' then 
					_single_col_extracter = '
						CASE 
							WHEN (srvc_req.form_data->''feedback_data'')::text NOT IN (''null'', '''') THEN 
								NULLIF(
									(
										srvc_req.form_data->''feedback_data''->''form_data''->>
										(srvc_req.form_data->''feedback_data''->>''rating_field_key'')
									)::text, ''''
								)::int
							ELSE NULL 
						END
					';

					-- raise notice 'INSIDE FEEDBACK : %', _single_col_extracter;
				elsif _single_col_details->>'key' = 'feedback_received' then 
					_single_col_extracter = 'CASE WHEN srvc_req.form_data->$$feedback_data$$ is not null THEN $$Yes$$ ELSE $$No$$ END';
				else
					_single_col_extracter = 'srvc_req.form_data->$$feedback_data$$->$$form_data$$#>>$${' || _single_col_key || '}$$';
				end if;
			
			end if;
		
			--For onfield task
			if is_onfield_task then 
			
				if _single_col_details->>'key' = 'assignee_name' then 
				
					_single_col_extracter = 'sbtsk.form_data->$$sbtsk_assignee$$->>$$label$$';
				
				elsif  _single_col_details->>'key' = 'task_status' then 
				
					_single_col_extracter = 'sbtsk_statuses.title';
				
				elsif  _single_col_details->>'key' = 'task_start_date' then 
				
					_single_col_extracter = 'sbtsk.form_data->>$$sbtsk_start_day$$';
				
				elsif  _single_col_details->>'key' = 'assigned_by' then 
				
					_single_col_extracter = 'assigned_by.name';
				
				elsif  _single_col_details->>'key' = 'assigned_date' then 
				
					_single_col_extracter = '(sbtsk.c_meta).time ';
				
				elsif  _single_col_details->>'key' = 'assigned_time' then 
				
					_single_col_extracter = '(sbtsk.c_meta).time ';
				    _single_col_extracter = ' TO_CHAR((' || _single_col_extracter || ')::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$, $$HH12:MIPM$$ )';
				
				elsif  _single_col_details->>'key' = 'task_status_remark' then 
				
					_single_col_extracter = ' sbtsk.form_data->$$update_type_data$$->sbtsk.status->>$$remarks$$ ';
				
				elsif  _single_col_details->>'key' = 'assignee_phone_no.' then 
				
					_single_col_extracter = 'assignee_phone_no.ind_id';
				
				elsif  _single_col_details->>'key' = 'sp_first_task_date' then 
				
					_single_col_extracter =  'first_sp_sbtsk.start_time';
				
				elsif  _single_col_details->>'key' = 'sp_last_task_date' then 
				
					_single_col_extracter =  'last_sp_sbtsk.start_time';
				
				elsif  _single_col_details->>'key' = 'gai_remarks' then 
				
					_single_col_extracter =  'sbtsk.form_data->$$gai_rating$$->$$gemini$$->>$$description$$';

				
				end if;
			end if;
		--org_info
			 if is_org_info then 
			 
					if _single_col_details->>'key' = 'brand_name' then 
					
						_single_col_extracter = 'org.nickname';
					
					elsif  _single_col_details->>'key' = 'service_type' then 
					
						_single_col_extracter = 'srvc_type.title';
					
					elsif  _single_col_details->>'key' = 'vertical_fr_req_dump' then 
					
					--if value = "[16,15]" then string_to_array convert this into = [16,15]
						_single_col_extracter = '(
													select key
						                             from jsonb_each($$'||all_verticals||'$$)
						                            where srvc_req.srvc_type_id = ANY (array(select jsonb_array_elements(value))::int[])
                                                )';
			--		raise notice '_single_col_details %',_single_col_details;
					
				end if;
			end if;
		
			--For transition_overview_fields
			if is_txn_overview then 
			
				_single_col_name = _single_col_name||'_TXN_DATE';
				_single_col_extracter = '( 
											select DATE(trnstn_log.trnstn_date) 
											  from cl_tx_srvc_req_trnstn_log as trnstn_log 
										     where trnstn_log.srvc_req_id = srvc_req.db_id
										       and trnstn_log.status_key = $$'|| _single_col_key || '$$ 
										  )';
			end if;
		
			if is_locked_for_change or is_sp_locked_for_change then
				if is_locked_for_change then 
					locked_for_change_column = 'srvc_req_lock';
				else 
					locked_for_change_column = 'sp_srvc_req_lock';
				end if;
				_single_col_extracter = ' CASE 
												 WHEN 
														(
															srvc_req.form_data->$$' || locked_for_change_column || '$$ is not null 
															and (srvc_req.form_data->$$' || locked_for_change_column || '$$)::bool is true
														) THEN $$Yes$$ 
											     ELSE 
													$$No$$
											     END';
			end if;
		    if is_sp_locked_for_change_by then
				_single_col_extracter = ' CASE 
												WHEN 
													(srvc_req.form_data->$$sp_srvc_req_lock$$)::bool is true
												THEN 
													(
														select user_name->>$$name$$ 
														  from json_array_elements($$'|| user_list || '$$) as user_name
			                                             where (srvc_req.form_data->>$$sp_srvc_req_locked_by$$) is not null 
														   and (srvc_req.form_data->>$$sp_srvc_req_locked_by$$) <> $$$$
														   and (user_name->>$$id$$)::uuid = (srvc_req.form_data->>$$sp_srvc_req_locked_by$$)::uuid
			                                             limit 1 
													)
												ELSE 
													$$$$
											END';

--		    	_single_col_extracter = ' CASE 
--												WHEN 
--													(srvc_req.form_data->$$sp_srvc_req_lock$$)::bool is true
--												THEN 
--													tms_get_user_details((srvc_req.form_data->>$$sp_srvc_req_locked_by$$)::uuid)->$$data$$->>$$user_name$$ 
--												ELSE 
--													$$$$
--											END';
		    end if;

			if is_sp_locking_date then
		   	    if _single_col_details->>'key' = 'sp_locking_date' then 
				   _single_col_extracter = 'CASE
					                          WHEN (srvc_req.form_data->>$$sp_srvc_req_lock$$)::bool 
												THEN srvc_req.form_data->>$$sp_srvc_req_locked_date_time$$
					                          ELSE NULL
					                        END';
				end if;
		    end if;
		
			--For Billing
			if is_calculated_taske then 
				if _single_col_details->>'key' = 'mandays' then 
					_single_col_extracter = 'tms_hlpr_get_sbtsk_count(srvc_req.db_id, $$' || org_id_ || '$$) ';
				end if;
			end if;
		
		    if is_calculated_taske then 
				if _single_col_details->>'key' = 'sp_work_progress' then 
					_single_col_extracter = 'tms_hlpr_get_sp_day_progress(srvc_req.db_id, $$' || org_id_ || '$$) ';
				end if;
			end if;
		
			if is_line_item then 
				if _single_col_details->>'key' = 'sp_total_quantity' then 
				
					_single_col_extracter = 'tms_hlpr_get_line_item_total_qty(srvc_req.form_data)';
				
				elsif  _single_col_details->>'key' = 'sp_total_price' then 
				
					_single_col_extracter = 'CASE 
												WHEN srvc_req.form_data->$$sp_line_items$$->>$$total$$ is not null
												THEN round((srvc_req.form_data->$$sp_line_items$$->>$$total$$)::numeric)
												ELSE $$ 0 $$ 
											 END';
				end if;
			end if;
		
			if is_pi_info then 
				if _single_col_details->>'key' = 'final_amount' then 
					_single_col_extracter = 'tms_hlpr_get_billing_final_amount($$' || org_id_ || '$$ ,srvc_req)';
				elseif _single_col_details->>'key' = 'sp_sent_for_billing' then 
					_single_col_extracter = 'CASE 
												WHEN srvc_req.form_data->>$$sp_send_for_billing$$ is not null THEN $$Yes$$
												ELSE $$No$$ 
											 END';
				end if;
			end if;
			
			if is_payout_info then 
				if _single_col_details->>'key' = 'total_quantity' then 
					_single_col_extracter = 'tms_hlpr_get_payout_qty($$' || org_id_ || '$$ ,srvc_req)';
				elseif _single_col_details->>'key' = 'total_price' then 
					_single_col_extracter = 'tms_hlpr_get_payout_rate($$' || org_id_ || '$$ ,srvc_req)';
				elseif _single_col_details->>'key' = 'total_payout' then 
					_single_col_extracter = 'tms_hlpr_get_payout_final_amount($$' || org_id_ || '$$ ,srvc_req)';
				end if;
			end if;
		
			if is_sp_authorities then 
			
				_single_col_extracter = '(
											select sp_authorities->>$$name$$ 
											  from json_array_elements($$'|| user_list || '$$) as sp_authorities 
                                             where (srvc_req.form_data->>$$' || _single_col_key || '$$) is not null 
											   and (srvc_req.form_data->>$$' || _single_col_key || '$$) <> $$$$
											   and (sp_authorities->>$$id$$)::uuid = (srvc_req.form_data->>$$' || _single_col_key || '$$)::uuid
                                             limit 1 
										 )';
										
--				_single_col_extracter = 'CASE 
--											WHEN srvc_req.form_data->>$$' || _single_col_key || '$$ is not null and srvc_req.form_data->>$$' || _single_col_key || '$$ <> $$$$
--											THEN tms_get_user_details((srvc_req.form_data->>$$' || _single_col_key || '$$)::uuid)->$$data$$->>$$user_name$$ 
--											ELSE $$$$ 
--										 END';
			end if;
			
			if is_brand_authorities then 
				
				_single_col_extracter = '(
											select sp_authorities->>$$name$$ 
											  from json_array_elements($$'|| user_list || '$$) as sp_authorities 
                                             where (srvc_req.form_data->>$$' || _single_col_key || '$$) is not null 
											   and (srvc_req.form_data->>$$' || _single_col_key || '$$) <> $$$$
											   and (sp_authorities->>$$id$$)::uuid = (srvc_req.form_data->>$$' || _single_col_key || '$$)::uuid
                                             limit 1 
										 )';
										
--				_single_col_extracter = 'CASE 
--											WHEN srvc_req.form_data->>$$' || _single_col_key || '$$ is not null and srvc_req.form_data->>$$' || _single_col_key || '$$ <> $$$$
--											THEN tms_get_user_details((srvc_req.form_data->>$$' || _single_col_key || '$$)::uuid)->$$data$$->>$$user_name$$ 
--											ELSE $$$$ 
--										 END';
			end if;
			if is_sp_attachment then
				_single_col_extracter = 'tms_hlpr_srvc_req_attachment_data_is_exists(srvc_req.form_data, $$' || _single_col_key || '$$)';
			end if;
		
		    if is_location_group then 
				if _single_col_details->>'key' = 'location_group' then 
					_single_col_extracter = 'tms_hlpr_get_loc_grps_name('|| org_id_ ||', srvc_req, $$'||(loc_groups_data)||'$$)';
				end if;
			end if;
		
			if _single_col_key = 'srvc_prvdr' then
				_single_col_extracter = 'srvc_prvdr_org.name_legal';
			end if;
		
			if _single_col_details->>'widget' = 'select'
			   or _single_col_details->>'widget'= 'radio-group'
			   or _single_col_details->>'widget'='checkbox-group'then 
	
			
--				array_to_string(array(
--										select value->>$$label$$
--										  from jsonb_array_elements(
--											  	 ( 
--													select value
--													  from jsonb_array_elements(((srvc_type.form_data#>>'{srvc_cust_fields_json}')::jsonb#>>'{translatedFields}')::jsonb)
--													 where $$15f81209-b9e0-4d00-8bbe-fa728f92c24f$$ = value->>$$key$$
--												  )->$$options$$	 
--											   ) 
--										 where srvc_req.form_data->>$$15f81209-b9e0-4d00-8bbe-fa728f92c24f$$ = value->>$$value$$
--										 	   or
--										 	   (
--										 	   		jsonb_typeof(srvc_req.form_data->$$15f81209-b9e0-4d00-8bbe-fa728f92c24f$$) = 'array'
--										 	   		and 
--										 	   		value->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(srvc_req.form_data->$$15f81209-b9e0-4d00-8bbe-fa728f92c24f$$ )))
--										 	   )
--								     ),',')

				if is_sp_custom_field is true then
					custom_field_column = '($$'|| sp_custom_fields_json ||'$$)';			    
				else
					custom_field_column = '((srvc_type.form_data#>>$${srvc_cust_fields_json}$$)::jsonb#>>$${translatedFields}$$)::jsonb';
				end if;		
			
				_single_col_extracter = 'array_to_string(array(
											select value->>$$label$$
											  from jsonb_array_elements(
												  	 ( 
														select value
														  from jsonb_array_elements('|| custom_field_column ||')
														 where $$'|| (_single_col_details->>'key')::text || '$$ = value->>$$key$$
													  )->$$options$$	 
												   ) 
											 where srvc_req.form_data#>>$${' || (_single_col_details->>'key')::text ||'}$$ = value->>$$value$$
                                                or (
                                                      jsonb_typeof(srvc_req.form_data->$$' || (_single_col_details->>'key')::text ||'$$) = $$array$$
                                                      and
                                                      value->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(srvc_req.form_data->$$' || (_single_col_details->>'key')::text ||'$$ )))
                                                   )
									     ),$$,$$)';
									
--				 _single_col_extracter = '( select value->>$$label$$ '|| 
--										 ' from json_array_elements($$' || (_single_col_details->'options')::text || '$$)' ||
--										 ' where srvc_req.form_data#>>$${' || (_single_col_details->>'key')::text ||'}$$ = value->>$$value$$' ||
--									      ')';
									     
			end if;
		
		
		
		
   			if _date_field_match_result = _single_col_key or  _date_field_match_result = _single_col_key or  _single_col_details->>'widget' = 'date-picker' then
			
   				_dynamic_columns = _dynamic_columns || ', ' || 'DATE((' || _single_col_extracter || ')::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$ )'  || ' as "' || _single_col_name || '" ';
		   	
		   	else 
	       		_dynamic_columns = _dynamic_columns || ', ' || _single_col_extracter  || ' as "' ||  _single_col_name || '" '; 
		    end if;
		   
	    END LOOP;
	   
	   	if srvc_type_id_ = 0 then
	   		_dynamic_columns = _dynamic_columns || ',  org.nickname as "ORDER_ASSIGNED_BY" '; 
	   	end if;
	   
	
	   _dynamic_sql = 'select json_agg(single_row) 
				       from (
								select srvc_req.display_code as "TMS ID",
									   srvc_status.title as "Status",
									   DATE((srvc_req.c_meta).time) as "C_DATE",
									   TO_CHAR(((srvc_req.c_meta).time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$, $$HH12:MIPM$$) as "C_Time",	
                                       c_by_usr."name" as "Added by", 
					                   txn_log_c_or_u_by."name" as "Last Updated By" ,
									   (
									    	CASE WHEN (DATE((last_srvc_txn_log.u_meta).time) IS NULL) THEN 
												DATE((last_srvc_txn_log.c_meta).time) 
											ELSE 
												DATE((last_srvc_txn_log.u_meta).time) 
											END
										) as "Last Updated Date",
										(
												CASE WHEN (DATE((last_srvc_txn_log.u_meta).time) IS NULL) THEN 
													TO_CHAR(((last_srvc_txn_log.c_meta).time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$, $$HH12:MIPM$$) 
												ELSE 
													TO_CHAR(((last_srvc_txn_log.u_meta).time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$, $$HH12:MIPM$$) 
												END
										) as "Last Updated Time"
										' || _dynamic_columns || '  
							      from public.cl_tx_srvc_req as srvc_req 
								 inner join cl_tx_srvc_req_trnstn_log as last_srvc_txn_log
						            on last_srvc_txn_log.db_id = (
							      		    select latest_srvc_txn_log.db_id 
								      		  from cl_tx_srvc_req_trnstn_log as latest_srvc_txn_log
								      		 where latest_srvc_txn_log.srvc_req_id = srvc_req.db_id
						 					   and srvc_req.status = latest_srvc_txn_log.status_key
								       		 order by (latest_srvc_txn_log.c_meta).time desc
								       		 limit 1
								       )
						 		 inner join cl_tx_users as txn_log_c_or_u_by 
								    on txn_log_c_or_u_by.usr_id  = case when (last_srvc_txn_log.u_by is null) then last_srvc_txn_log.c_by else last_srvc_txn_log.u_by end
								 inner join cl_cf_service_types as srvc_type
							        on srvc_type.service_type_id = srvc_req.srvc_type_id 
							     inner join cl_tx_orgs as org
							        on srvc_req.org_id = org.org_id 
							     inner join cl_cf_srvc_statuses as srvc_status
							        on srvc_status.srvc_id = srvc_req.srvc_type_id 
							       and srvc_status.status_key = srvc_req.status 
							     inner join cl_tx_users c_by_usr 
							        on c_by_usr.usr_id = srvc_req.c_by
								  left join cl_tx_sbtsk as sbtsk 
								    on sbtsk.db_id = (
							      		select latest_sbtsk.db_id 
							      		  from cl_tx_sbtsk as latest_sbtsk
							      		 where latest_sbtsk.srvc_req_id = srvc_req.db_id
										   and latest_sbtsk.is_deleted is not true
										   and latest_sbtsk.sbtsk_type = ANY(array['|| array_to_string(onfield_subtask_types,',') ||'])
                                           and (
												  ' || srvc_type_id_ || ' <> 0 
												  or latest_sbtsk.org_id = '|| org_id_ ||'
											   )
							       		 order by latest_sbtsk.start_time desc
							       		 limit 1
								       )
								  left join cl_tx_sbtsk as first_sp_sbtsk
								    on first_sp_sbtsk.db_id = (
							      		select earliest_sbtsk.db_id 
							      		  from cl_tx_sbtsk as earliest_sbtsk
							      		 where earliest_sbtsk.srvc_req_id = srvc_req.db_id
				   						   and earliest_sbtsk.org_id = srvc_req.srvc_prvdr
										   and earliest_sbtsk.is_deleted is not true
										   and earliest_sbtsk.sbtsk_type = ANY(array['|| array_to_string(onfield_subtask_types,',') ||'])
							       		 order by earliest_sbtsk.start_time
							       		 limit 1
								       )
								  left join cl_tx_sbtsk as last_sp_sbtsk 
								    on last_sp_sbtsk.db_id = (
							      		select latest_sbtsk.db_id 
							      		  from cl_tx_sbtsk as latest_sbtsk
							      		 where latest_sbtsk.srvc_req_id = srvc_req.db_id
				   						   and latest_sbtsk.org_id = srvc_req.srvc_prvdr
										   and latest_sbtsk.is_deleted is not true
										   and latest_sbtsk.sbtsk_type = ANY(array['|| array_to_string(onfield_subtask_types,',') ||'])
							       		 order by latest_sbtsk.start_time desc
							       		 limit 1
								       )
								  '|| gai_rating_join || srvc_open_status_trxn_join ||'
								  left join cl_cf_sbtsk_statuses as sbtsk_statuses
						            on sbtsk_statuses.status_key = sbtsk.status
						           and sbtsk_statuses.sbtsk_type_id = sbtsk.sbtsk_type
								  left join cl_tx_users assigned_by 
							        on assigned_by.usr_id = sbtsk.c_by
                                  left join cl_tx_usr_identities as assignee_phone_no
								    on assignee_phone_no.user_id = any(sbtsk.assigned_to)
                                   and assignee_phone_no.ind_type = $$MOBILE_NUM$$
								 '|| vertical_list_join ||'
								 left join cl_tx_orgs as srvc_prvdr_org
								    on srvc_prvdr_org.org_id = srvc_req.srvc_prvdr
								 where srvc_req.db_id in (' || array_to_string(matching_ids,',') || ')
								 group by org.org_id,c_by_usr."name",srvc_status.db_id ,srvc_req.db_id , sbtsk.db_id, sbtsk_statuses.db_id, 
								 srvc_type.service_type_id, assigned_by.usr_id, last_srvc_txn_log.db_id, txn_log_c_or_u_by.usr_id,assignee_phone_no.id,
								 first_sp_sbtsk.start_time ,last_sp_sbtsk.start_time , srvc_prvdr_org.org_id  '|| additional_group_by||'
								 order by '|| additional_order_by ||'
							 ) single_row 
					';
 	
		raise notice 'dump sql %', _dynamic_sql;
		
		return query execute _dynamic_sql;
	END;
$function$
;
