CREATE OR REPLACE FUNCTION public.tms_get_subtasks_details(form_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	DECLARE
    status BOOLEAN := false;
    message TEXT := 'Internal_error';		
    resp_data JSON := '{}'::json;
    -- offset_count INTEGER := (page_no - 1) * page_size; 
    _srvc_prvdr_id integer;
    _srvc_req_id text;
    _sbtsk_id text;
   
   	sbtsk_typ_id_vs_strt_status jsonb;
    
begin
	_srvc_prvdr_id = form_data->>'srvc_prvdr_id'; 
	_srvc_req_id = form_data->>'srvc_req_id';
	_sbtsk_id = form_data->>'sbtsk_id';

	select json_object_agg(
	           sbtsk_type.sbtsk_type_id,
	           jsonb_build_object(
	               'sbtsk_type_id', sbtsk_type.sbtsk_type_id,
	               'status_key', sbtsk_status.status_key,
	               'title', sbtsk_status.title
	           )
	       )
	  from cl_cf_sbtsk_types sbtsk_type
	 inner join cl_cf_sbtsk_statuses sbtsk_status
	    on sbtsk_status.sbtsk_type_id = sbtsk_type.sbtsk_type_id 
       and sbtsk_status.status_key = sbtsk_type.form_data->>'sbtsk_status_for_attendance'
      into sbtsk_typ_id_vs_strt_status;

	select json_agg(t.*)
      into resp_data
      from (
		select srvc_req.db_id "srvc_req_db_id",
			   sbtsk.db_id "sbtsk_db_id",
               srvc_req.display_code "Order ID",
		       sbtsk.org_id "org_id",
		       sbtsk.sbtsk_type "sbtsk_type_id",
		      --  org.nickname "Brand",
		       srvc_req.form_data->>'request_description' "Description",
		       sbtsk_sts.title "Subtask status",
		       sbtsk_sts.status_type "Status type",
			   sbtsk.start_time "Pl. start time",
			   sbtsk_txn_attnd.trnstn_date "Start time",
			   sbtsk_typ_id_vs_strt_status->sbtsk_type.sbtsk_type_id->>'title' as "Start status",
			   sbtsk.end_time  "Pl. end time",
			   sbtsk_txn_cls.trnstn_date "End time",
			   sbtsk_cls_status.title "End status",
			   sbtsk.form_data->'update_type_data'->(sbtsk_type.form_data->>'sbtsk_status_for_attendance')->>'remarks' "Start remarks",
			   sbtsk.form_data->'update_type_data'->(sbtsk_type.form_data->>'sbtsk_status_for_attendance')->'attachments' "Start files",
			   sbtsk.form_data->'update_type_data'->(sbtsk_txn_cls.status_key)->'remarks' "End remarks",
			   sbtsk.form_data->'update_type_data'->(sbtsk_txn_cls.status_key)->'attachments' "End files",
			   srvc_req.form_data->'feedback_data' "Feedback data",
			   sbtsk_type.form_data,
			   sbtsk_type.form_data->>'sbtsk_status_for_attendance',
		       tms_hlpr_get_org_timezone(sbtsk.org_id) "org_timezone"
--			   (
--				   CASE 
--			           WHEN sbtsk_type.form_data->>( 'sbtsk_status_' ||  (sbtsk_type.form_data->>'sbtsk_status_for_attendance')  ||  '_fields'  ) = ''
--			               OR 
--			                sbtsk_type.form_data->>( 'sbtsk_status_' ||  (sbtsk_type.form_data->>'sbtsk_status_for_attendance')  ||  '_fields'  ) IS NULL THEN
--			           	'{}'
--			           ELSE
--			             sbtsk_type.form_data->>( 'sbtsk_status_' ||  (sbtsk_type.form_data->>'sbtsk_status_for_attendance')  ||  '_fields'  )
--			       end
--		       )::json->'translatedFields' "start status key map",
--		       (
--				   CASE 
--			           WHEN sbtsk_type.form_data->>( 'sbtsk_status_' ||  sbtsk_cls_status.status_key  ||  '_fields'  ) = ''
--			               OR 
--			                sbtsk_type.form_data->>( 'sbtsk_status_' ||  sbtsk_cls_status.status_key  ||  '_fields'  ) IS NULL THEN
--			           	'{}'
--			           ELSE
--			             sbtsk_type.form_data->>( 'sbtsk_status_' ||  sbtsk_cls_status.status_key  ||  '_fields'  )
--			       end
--		       )::json->'translatedFields' "end status key map"
  		  from cl_cf_service_types service_types 
         inner join cl_tx_srvc_req srvc_req 
            on srvc_req.srvc_type_id = service_types.service_type_id 
           and srvc_req.is_deleted is not true
           and (
		    		_srvc_req_id is null
		    		or
		    		srvc_req.db_id = _srvc_req_id::bigint
  	           ) 
 	     inner join cl_tx_sbtsk sbtsk
            on sbtsk.srvc_req_id = srvc_req.db_id
           and sbtsk.is_deleted is not true
           and (sbtsk.form_data->>'gai_rating' is null or sbtsk.form_data->'gai_rating'->> 'gemini' is null or sbtsk.form_data->'gai_rating'->'gemini'->>'rating' is null)  -- Getting subtasks which are not rated
          --  and (sbtsk.form_data->>'relative_gai_rating' is null or sbtsk.form_data->'relative_gai_rating'->> 'gemini' is null or sbtsk.form_data->'relative_gai_rating'->'gemini'->>'rating' is null)  -- Getting subtasks which are not rated 
		   and sbtsk.status = 'closed'
	       and (
				    _sbtsk_id is null
				    or
				    sbtsk.db_id = _sbtsk_id::bigint
			   )
         inner join cl_cf_sbtsk_types sbtsk_type
            on sbtsk_type.sbtsk_type_id = sbtsk.sbtsk_type
           and sbtsk_type.start_status_key is not null
--		  -- Current status
         inner join cl_cf_sbtsk_statuses sbtsk_sts
            on sbtsk_sts.sbtsk_type_id = sbtsk.sbtsk_type 
           and sbtsk_sts.status_key = sbtsk.status  
--          -- Start status related
         inner join cl_tx_sbtsk_trnstn_log sbtsk_txn_attnd
            on sbtsk_txn_attnd.sbtsk_id = sbtsk.db_id 
           and sbtsk_txn_attnd.status_key = sbtsk_type.start_status_key
--         inner join cl_cf_sbtsk_statuses sbtsk_strt_status
--            on sbtsk_strt_status.sbtsk_type_id = sbtsk.sbtsk_type 
--           and sbtsk_type.form_data->>'sbtsk_status_for_attendance' is not null
--           and sbtsk_strt_status.status_key = sbtsk_type.start_status_key 
--          -- Closure status related
         inner join cl_tx_sbtsk_trnstn_log sbtsk_txn_cls
            on sbtsk_txn_cls.sbtsk_id = sbtsk.db_id 
           and sbtsk_txn_cls.status_key = 'closed'
         inner join cl_cf_sbtsk_statuses sbtsk_cls_status
            on sbtsk_cls_status.sbtsk_type_id = sbtsk.sbtsk_type 
           and sbtsk_cls_status.status_key = 'closed' 
         where service_types.form_data->>'gai_rating_subtasks' = 'true'
				   and DATE((srvc_req.c_meta).time) >= DATE('2025-01-01')
					--  and sbtsk.db_id = 1526155
          --  and DATE((srvc_req.c_meta).time) <= DATE('2024-05-15')
--    srvc_req.srvc_prvdr = srvc_prvdr_id
        order by srvc_req.db_id desc
       limit 50
   ) t;
  
  	status := true;
    message := 'success';
  	return json_build_object('status',true,'code','success','data',resp_data);
		
END;
$function$
;
 