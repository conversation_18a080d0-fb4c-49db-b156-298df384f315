CREATE OR REPLACE FUNCTION public.tms_get_jiffy_usage_daily(requester_info json, filter_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
-- 	Bare minimums
	status boolean;
	message text;
	resp_data json;
	org_id_ int;

	filter_transition_date text[];
    filter_transition_date_from timestamp;
    filter_transition_date_to timestamp;

	begin
		status = false;
		message = 'Internal_error';
	
		org_id_ = json_extract_path_text(requester_info,'org_id');	
       
		filter_transition_date = array(select json_array_elements_text(filter_->'date_range'))::text[];
	 	filter_transition_date_from =  DATE(filter_transition_date[1]::timestamp);
		filter_transition_date_to   =  DATE(filter_transition_date[2]::timestamp);

		resp_data = array_to_json(array( select 
				    json_build_object(
				        'Status update(overall)', COUNT(*),
				        'Vertical', ctos.settings_data->>'vertical_title',
				        'Status Update (Via JIFFY)', COUNT(*) FILTER (
				            WHERE srvc_req_trnstn_log.is_updated_via_jiffy = true
				        ),
				        'JIFFY Utilization',
				        ROUND(
				            (
				                COUNT(*) FILTER (
				                    WHERE srvc_req_trnstn_log.is_updated_via_jiffy = true
				                )::numeric
				                / COUNT(*)::numeric
				            ) * 100,
				            2
				        )
				    )
				
				FROM cl_tx_srvc_req_trnstn_log AS srvc_req_trnstn_log
				INNER JOIN cl_tx_orgs_settings AS ctos
				    ON ctos.org_id = 2
				    AND ctos.settings_type = 'SP_CUSTOM_FIELDS'
				    AND srvc_req_trnstn_log.srvc_type_id = ANY (
				        ARRAY(
				            SELECT json_array_elements_text(ctos.settings_data->'srvc_type_id')
				        )::integer[]
				    )
				WHERE DATE(srvc_req_trnstn_log.trnstn_date) >= DATE(filter_transition_date_from)
				  AND DATE(srvc_req_trnstn_log.trnstn_date) <= DATE(filter_transition_date_to)
				GROUP BY ctos.db_id));
			

		status = true;
		message = 'success';

		return jsonb_build_object('status',status,'code',message,'data',resp_data);
	END;
$function$
;
