var sampleOperationResp = require('./utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('./utils/db_resp');
const users_model = require('./users_model');
const path = require('path');
var AWS = require('aws-sdk');
const common = require('./utils/common');

class media_service_model {
    getFile(fileName, query, res, req) {
        this.initS3();

        query['org_id'] = users_model.getOrgId(this.userContext);
        query['usr_id'] = users_model.getUUID(this.userContext); // this can be null
        query['ip_address'] = this.ip_address;
        query['user_agent'] = this.user_agent_;
        const mode = query.mode || 'Preview';
        // let final_file_name = decodeURI(path.basename(fileName)); // cleaning
        // // avoiding access to other org
        // final_file_name =
        //     'org_' + query['org_id'] + '/' + final_file_name;

        // remove '/' from the fileName
        // and allowing direct access to file
        // In future should be made more secured for Service providers and customers to share file
        let final_file_name = decodeURI(fileName.substring(1));

        this.s3Params['Key'] = final_file_name;
        // Set disposition based on mode
        if (mode.toLowerCase() === 'download') {
            this.s3Params.ResponseContentDisposition = `attachment; filename="${path.basename(final_file_name)}"`;
        }
        this.s3.getSignedUrl('getObject', this.s3Params, function (err, url) {
            if (err) {
                res.status(HttpStatus.StatusCodes.BAD_REQUEST).send(
                    'File does not exist'
                );
            } else {
                res.status(HttpStatus.StatusCodes.SEE_OTHER).redirect(url);
            }
        });
    }

    getSignedUrl(query) {
        return new Promise((resolve, reject) => {
            if (!query['file_name']) {
                return resolve(
                    new sampleOperationResp(
                        false,
                        'Could not create a url for you - file name not found. Request query => ' +
                            JSON.stringify(query),
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }

            this.initS3();

            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext); // this can be null
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            let file_name = path.basename(
                query['file_name'],
                path.extname(query['file_name'])
            ); // cleaning
            let file_extension = path.extname(query['file_name']);
            const isPdf = file_extension === '.pdf';
            let final_file_name =
                file_name +
                '_' +
                process.hrtime.bigint() +
                '_' +
                common.genJti() +
                file_extension;

            const key = 'org_' + query['org_id'] + '/' + final_file_name;

            // Set fields
            this.s3Params['Expires'] = 300;
            this.s3Params['Fields'] = {};
            this.s3Params['Fields']['key'] =
                'org_' + query['org_id'] + '/' + final_file_name;

            // Only add Content-Type and Content-Disposition for PDFs
            if (isPdf) {
                this.s3Params['Fields']['Content-Type'] = 'application/pdf';
                this.s3Params['Fields']['Content-Disposition'] = 'inline';
                // Add policy conditions
                this.s3Params['Conditions'] = [
                    { key: key },
                    { 'Content-Type': 'application/pdf' },
                    ['starts-with', '$Content-Disposition', 'inline'],
                ];
            }

            this.s3.createPresignedPost(this.s3Params, function (err, data) {
                if (err) {
                    console.error(
                        'Presigning post data encountered an error',
                        err
                    );
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Could not create a url for you - ' +
                                JSON.stringify(err),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            false,
                            JSON.stringify(data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            });
        });
    }

    initS3() {
        const credentials = {
            accessKeyId: process.env.S3_ACCESS_KEY,
            secretAccessKey: process.env.S3_SECRET_KEY,
        };
        AWS.config.update({
            credentials: credentials,
            region: process.env.S3_REGION,
        });
        this.s3 = new AWS.S3();

        this.s3Params = {
            Bucket: process.env.S3_BUCKET,
        };
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    getInstance() {
        const instance = new media_service_model();
        return instance;
    }

    getFreshInstance(model) {
        const clonedInstance = new media_service_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new media_service_model();
