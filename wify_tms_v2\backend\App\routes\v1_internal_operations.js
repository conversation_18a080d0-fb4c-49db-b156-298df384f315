const express = require('express');
const internal_operations_model = require('../api_models/internal_operations_model');
const router = express.Router();

router.get(
    `/geo-status/${process.env.PENDING_SUBTASKS_GEO_VERIFICATION_STATUS_URL}`,
    function (req, res, next) {
        const form_data = {
            org_id: '2',
            from_date: '2025-01-21',
            to_date: '2025-02-26',
            current_geo_status: 'pending',
        };
        const model = setParamsToModel(req);
        req = { ...req, ...form_data };
        model.refreshGeoStatus(req).then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
    }
);
router.get(
    `/daily-usage/${process.env.GET_JIFFY_DAILY_USAGE_URL}`,
    function (req, res, next) {
        const form_data = {
            org_id: '2',
        };
        const model = setParamsToModel(req);
        model.getJiffyDailyUsage(form_data).then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
    }
);

router.post(
    `/delete-api-logs/${process.env.DELETE_EXTERNAL_API_LOGS_URL}`,
    function (req, res, next) {
        const model = setParamsToModel(req);
        model.deleteExternalApiLogs(req.body).then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
    }
);

const setParamsToModel = (req) => {
    const internal_operations_model_instance =
        internal_operations_model.getInstance();
    internal_operations_model_instance.database = req.app.get('db');
    internal_operations_model_instance.ip_addr = req.ip;
    internal_operations_model_instance.user_agent = req.get('User-Agent');
    // internal_operations_model_instance.user_context = getUserContextFrmReq(req);
    return internal_operations_model_instance;
};

module.exports = router;
