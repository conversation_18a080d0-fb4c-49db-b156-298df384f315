{"name": "app", "version": "0.0.0", "private": true, "scripts": {"start": "nodemon ./bin/www", "install-start": "npm install && node ./bin/www", "sync-start": "npm run install-start", "start-uat": "cross-env NODE_ENV=test npm run sync-start", "start-dev": "cross-env NODE_ENV=dev npm run sync-start", "start-uat-check": "cross-env NODE_ENV=test npm start", "start-prod": "cross-env NODE_ENV=production npm run sync-start", "start-prod-check": "cross-env NODE_ENV=production npm start", "coverage": "jest --coverage", "test": "jest", "prettier-format": "prettier --write ."}, "dependencies": {"JSONStream": "^1.3.5", "aws-sdk": "2.908.0", "axios": "^0.26.0", "bcrypt": "^5.1.1", "body-parser": "^1.18.3", "bull": "^3.22.9", "cookie-parse": "^0.4.0", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "cross-env": "^7.0.3", "crypto": "^1.0.1", "csv-parser": "^3.0.0", "debug": "~2.6.9", "dotenv": "^8.2.0", "event-stream": "^4.0.1", "express": "~4.16.1", "firebase-admin": "^11.9.0", "form-data": "^4.0.1", "helmet": "6.1.0", "http-errors": "~1.6.3", "http-proxy": "^1.18.1", "http-status-codes": "^2.1.4", "is-empty": "^1.2.0", "jade": "~1.11.0", "json-rules-engine": "^6.1.2", "json-to-csv-stream": "^1.1.0", "json2csv": "^6.0.0-alpha.2", "json5": "^2.2.3", "jsonwebtoken": "^8.5.1", "kafkajs": "^1.15.0", "massive": "^6.7.1", "massive-migrate": "^0.10.0", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "morgan": "~1.9.1", "newrelic": "^10.1.2", "nodemailer": "^6.6.5", "nodemon": "^2.0.15", "pg": "^8.6.0", "postgres-migrations": "^5.2.0", "prom-client": "^15.1.3", "prompt-confirm": "^2.0.4", "redis": "^3.1.0", "request": "^2.88.2", "string-similarity": "^4.0.4", "xml2js": "^0.6.2"}, "devDependencies": {"jest": "^29.7.0", "prettier": "^3.3.3", "supertest": "^7.0.0"}, "nodemonConfig": {"ignore": ["temp_files"]}}