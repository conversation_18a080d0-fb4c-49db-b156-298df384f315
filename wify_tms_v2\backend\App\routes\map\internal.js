const version = 'v1';
const prefix = 'internal-operations';

module.exports = ({
    AUTH_ADMIN_MIDDLEWARE,
    AUTH_MIDDLEWARE,
    AUTH_CONSUMER_FEEDBACK_MIDDLEWARE,
}) => {
    return [
        {
            path: `/${version}/${prefix}/refresh`,
            route_file: './routes/v1_internal_operations.js',
            // middleware: AUTH_ADMIN_MIDDLEWARE,
        },
        {
            path: `/${version}/${prefix}/pnl-cron`,
            route_file: './routes/v1_services',
        },
        {
            path: `/${version}/${prefix}/jiffy`,
            route_file: './routes/v1_internal_operations.js',
        },
    ];
};
